using OrderFlowCore.Application.Interfaces.Services;
using System.Security.Claims;

namespace OrderFlowCore.Web.ViewModels
{
    public class HRCoordinatorDashboardViewModel
    {
        public string Username { get; set; } = "";
        public string Role { get; set; } = "";
        public string Email { get; set; } = "";
        public HRCoordinatorStatisticsDto Statistics { get; set; } = new();
        public bool HasError { get; set; }
        public string ErrorMessage { get; set; } = "";
    }

   

    public class CoordinatorWorkflowStageDto
    {
        public string StageName { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
        public string StageColor { get; set; } = "";
        public string Description { get; set; } = "";
    }

    public class SupervisorPerformanceDto
    {
        public string SupervisorName { get; set; } = "";
        public string Department { get; set; } = "";
        public int TotalOrdersAssigned { get; set; }
        public int OrdersCompleted { get; set; }
        public int OrdersPending { get; set; }
        public int OrdersRejected { get; set; }
        public double CompletionRate { get; set; }
        public double AverageProcessingTime { get; set; } // in hours
    }

    public class RecentActivityDto
    {
        public int OrderId { get; set; }
        public string EmployeeName { get; set; } = "";
        public string Department { get; set; } = "";
        public string Action { get; set; } = "";
        public string ActionDescription { get; set; } = "";
        public DateTime Timestamp { get; set; }
        public string Status { get; set; } = "";
        public string Icon { get; set; } = "";
        public string Color { get; set; } = "";
    }

    public class OrderStatusCountDto
    {
        public string Status { get; set; } = "";
        public string StatusDisplayName { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
        public string Color { get; set; } = "";
    }

    public class DepartmentOrderCountDto
    {
        public string DepartmentName { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
        public int PendingCount { get; set; }
        public int CompletedCount { get; set; }
    }
} 