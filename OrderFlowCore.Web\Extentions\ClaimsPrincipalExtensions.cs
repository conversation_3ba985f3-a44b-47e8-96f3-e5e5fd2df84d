﻿using System.Security.Claims;

namespace OrderFlowCore.Web.Extentions;

// Extension methods for retrieving claims
public static class ClaimsPrincipalExtensions
{
    private const string ROLE_TYPE_CLAIM = "RoleType";
    private const string EMAIL_CLAIM = "Email";

    public static string GetUserRoleType(this ClaimsPrincipal principal)
    {
        return principal.FindFirst(ROLE_TYPE_CLAIM)?.Value ?? string.Empty;
    }

    public static string GetEmail(this ClaimsPrincipal principal)
    {
        return principal.FindFirst(EMAIL_CLAIM)?.Value ??
               principal.FindFirst(ClaimTypes.Email)?.Value ?? string.Empty;
    }

    public static string GetUserId(this ClaimsPrincipal principal)
    {
        return principal.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? string.Empty;
    }

    public static int GetUserIdAsInt(this ClaimsPrincipal principal)
    {
        var userIdString = principal.GetUserId();
        return int.TryParse(userIdString, out int userId) ? userId : 0;
    }

    public static string GetUserRole(this ClaimsPrincipal principal)
    {
        return principal.FindFirst(ClaimTypes.Role)?.Value ?? string.Empty;
    }

    public static string GetUsername(this ClaimsPrincipal principal)
    {
        return principal.Identity?.Name ?? string.Empty;
    }

    public static bool HasRoleType(this ClaimsPrincipal principal, string roleType)
    {
        var userRoleType = principal.GetUserRoleType();
        return !string.IsNullOrEmpty(userRoleType) &&
               userRoleType.Equals(roleType, StringComparison.OrdinalIgnoreCase);
    }

    public static DateTimeOffset? GetAuthenticationTime(this ClaimsPrincipal principal)
    {
        var authTimeString = principal.FindFirst("AuthenticationTime")?.Value;
        return DateTimeOffset.TryParse(authTimeString, out var authTime) ? authTime : null;
    }
}
