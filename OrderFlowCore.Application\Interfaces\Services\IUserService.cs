using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IUserService
{
    Task<ServiceResult<UserProfileDto>> GetProfileAsync(int userId);
    Task<ServiceResult> UpdateProfileAsync(UserProfileDto dto);
    Task<ServiceResult> ChangePasswordAsync(int userId, ChangePasswordDto dto);
    
    // Account Management Methods
    Task<ServiceResult<List<UserDto>>> GetAllUsersAsync();
    Task<ServiceResult<UserDto>> GetUserByUsernameAsync(string username);
    Task<ServiceResult<UserDto>> CreateUserAsync(UserDto userDto);
    Task<ServiceResult<UserDto>> UpdateUserAsync(UserDto userDto);
    Task<ServiceResult> DeleteUserAsync(string username);
    Task<ServiceResult> SetUserPasswordAsync(string username, string newPassword);
}
