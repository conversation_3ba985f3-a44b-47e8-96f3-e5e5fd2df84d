using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IOrdersTypeService
{
    Task<ServiceResult<IEnumerable<OrdersTypeDto>>> GetAllAsync();
    Task<ServiceResult<OrdersTypeDto>> GetByIdAsync(int id);
    Task<ServiceResult> CreateAsync(OrdersTypeDto dto);
    Task<ServiceResult> UpdateAsync(OrdersTypeDto dto);
    Task<ServiceResult> DeleteAsync(int id);
}
