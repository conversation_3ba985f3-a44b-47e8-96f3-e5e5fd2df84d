@model OrderFlowCore.Application.DTOs.DepartmentDto
@{
    ViewData["Title"] = "تعديل القسم";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">تعديل القسم</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="@Url.Action("Index", "Dashboard")">لوحة التحكم</a></li>
                        <li class="breadcrumb-item"><a href="@Url.Action("Index", "Department")">الأقسام</a></li>
                        <li class="breadcrumb-item active" aria-current="page">تعديل القسم</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">معلومات القسم</h5>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="CreatedAt" />
                        
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Name" class="form-label">اسم القسم <span class="text-danger">*</span></label>
                                <input asp-for="Name" class="form-control" placeholder="أدخل اسم القسم" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label asp-for="IsActive" class="form-label">الحالة</label>
                                <div class="form-check form-switch">
                                    <input asp-for="IsActive" class="form-check-input" />
                                    <label asp-for="IsActive" class="form-check-label">نشط</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label asp-for="Description" class="form-label">الوصف</label>
                            <textarea asp-for="Description" class="form-control" rows="4" 
                                      placeholder="أدخل وصف القسم (اختياري)"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>
                        
                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                تاريخ الإنشاء: @Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")
                            </small>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>عودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
} 