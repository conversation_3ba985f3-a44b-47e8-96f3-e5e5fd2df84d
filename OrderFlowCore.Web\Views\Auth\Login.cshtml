﻿@model OrderFlowCore.Application.DTOs.LoginDto
@{
    ViewData["Title"] = "تسجيل الدخول";
    Layout = "_AuthLayout";
}

<div class="login-container">
    <div class="card shadow-lg border-0 rounded-4">
        <div class="card-body p-5">
            <h2 class="text-center fw-bold mb-4 text-primary display-5">تسجيل الدخول للنظام</h2>

            @if (TempData["RegisterMessage"] != null)
            {
                <div class="alert alert-info text-center">
                    @TempData["RegisterMessage"]
                </div>
            }

            @if (ViewData.ModelState.ErrorCount > 0)
            {
                <div asp-validation-summary="All" class="alert alert-danger text-center"></div>
            }

            <form asp-action="Login" method="post">
                <input type="hidden" asp-for="ReturnUrl" value="@ViewData["ReturnUrl"]" />
                
                <div class="mb-4">
                    <label asp-for="Username" class="form-label fw-semibold fs-4">اسم المستخدم</label>
                    <div class="input-group input-group-lg">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                        <input asp-for="Username" class="form-control fs-5" placeholder="أدخل اسم المستخدم" />
                    </div>
                    <span asp-validation-for="Username" class="text-danger fs-5 mt-2"></span>
                </div>

                <div class="mb-4">
                    <label asp-for="Password" class="form-label fw-semibold fs-4">كلمة المرور</label>
                    <div class="input-group input-group-lg">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        <input asp-for="Password" type="password" class="form-control fs-5" id="password" placeholder="أدخل كلمة المرور" />
                        <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility()">
                            <i class="fas fa-eye fs-5" id="password-toggle-icon"></i>
                        </button>
                    </div>
                    <span asp-validation-for="Password" class="text-danger fs-5 mt-2"></span>
                </div>

                <div class="mb-4">
                    <div class="form-check">
                        <input asp-for="RememberMe" class="form-check-input" id="rememberMe" />
                        <label asp-for="RememberMe" class="form-check-label fs-5" for="rememberMe">
                            تذكرني
                        </label>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary w-100 py-3 fw-bold mb-3 fs-4">
                    <i class="fas fa-sign-in-alt me-2"></i> تسجيل الدخول
                </button>
            </form>
        </div>
    </div>
</div>

<style>
    .login-container {
        max-width: 600px;
        width: 100%;
        margin: 0 auto;
    }

    .card {
        background-color: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        overflow: hidden;
        backdrop-filter: blur(10px);
    }

    .form-control {
        border-radius: 8px;
        padding: 15px;
        height: auto;
        font-size: 18px;
        transition: all 0.3s;
        border: 2px solid #e9ecef;
    }

    .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        transform: translateY(-1px);
    }

    .input-group-text {
        background-color: #f8f9fa;
        border-color: #dee2e6;
        font-size: 18px;
        padding: 0 15px;
        border: 2px solid #e9ecef;
    }

    .btn-primary {
        background: linear-gradient(45deg, #2937f0, #9f1ae2);
        border: none;
        border-radius: 8px;
        font-size: 20px;
        transition: all 0.3s;
        position: relative;
        overflow: hidden;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
        background: linear-gradient(45deg, #1e2bd1, #8614c0);
    }

    .btn-primary:active {
        transform: translateY(0);
    }

    .btn-outline-secondary {
        border: 2px solid #dee2e6;
        transition: all 0.3s;
    }

    .btn-outline-secondary:hover {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
    }

    /* Animation for form elements */
    .card {
        animation: fadeIn 0.8s ease-in-out;
    }

    @@keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* For error messages */
    .text-danger {
        font-size: 16px;
        margin-top: 5px;
        display: block;
        animation: shake 0.5s ease-in-out;
    }

    @@keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    /* Make validation summary look better */
    .validation-summary-errors ul {
        list-style: none;
        padding-right: 0;
        margin-bottom: 0;
        font-size: 16px;
    }

    /* Form check styling */
    .form-check-input {
        width: 20px;
        height: 20px;
        border: 2px solid #dee2e6;
        transition: all 0.3s;
    }

    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
        transform: scale(1.1);
    }

    .form-check-label {
        margin-right: 10px;
        cursor: pointer;
        transition: color 0.3s;
    }

    .form-check-label:hover {
        color: #0d6efd;
    }

    /* Alert styling */
    .alert {
        border-radius: 8px;
        border: none;
        font-size: 16px;
        animation: slideIn 0.5s ease-in-out;
    }

    @@keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive design */
    @@media (max-width: 768px) {
        .login-container {
            max-width: 100%;
            padding: 0 20px;
        }
        
        .card-body {
            padding: 2rem !important;
        }
        
        .display-5 {
            font-size: 2rem;
        }
        
        .fs-4 {
            font-size: 1.1rem !important;
        }
        
        .fs-5 {
            font-size: 1rem !important;
        }
    }

    /* Loading animation for button */
    .btn-primary.loading {
        pointer-events: none;
    }

    .btn-primary.loading::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        top: 50%;
        left: 50%;
        margin-left: -10px;
        margin-top: -10px;
        border: 2px solid transparent;
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @@keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<script>
    function togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('password-toggle-icon');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    // Show validation summary if it contains errors
    document.addEventListener('DOMContentLoaded', function() {
        const validationSummary = document.querySelector('.validation-summary-errors');
        if (validationSummary) {
            validationSummary.closest('.alert').classList.remove('d-none');
        }

        // Add loading state to submit button
        const form = document.querySelector('form');
        const submitBtn = form.querySelector('button[type="submit"]');
        
        form.addEventListener('submit', function() {
            submitBtn.classList.add('loading');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري تسجيل الدخول...';
        });

        // Add focus effects to form controls
        const formControls = document.querySelectorAll('.form-control');
        formControls.forEach(control => {
            control.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            
            control.addEventListener('blur', function() {
                this.parentElement.classList.remove('focused');
            });
        });
    });

</script>