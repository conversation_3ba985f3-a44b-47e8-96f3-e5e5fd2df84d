using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;

namespace OrderFlowCore.Application.Services
{
    public class EmployeeService : IEmployeeService
    {
        private readonly IUnitOfWork _unitOfWork;
        
        public EmployeeService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        
        public async Task<ServiceResult<EmployeeDto>> GetByCivilNumberAsync(string civilNumber)
        {
            try
            {
                var employee = await _unitOfWork.Employees.GetByCivilNumberAsync(civilNumber);
                if (employee == null)
                    return ServiceResult<EmployeeDto>.Failure("الموظف غير موجود");

                return ServiceResult<EmployeeDto>.Success(employee);
            }
            catch (Exception ex)
            {
                return ServiceResult<EmployeeDto>.Failure($"خطأ في استرجاع الموظف: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult<IEnumerable<EmployeeDto>>> GetAllAsync()
        {
            try
            {
                var employees = await _unitOfWork.Employees.GetAllAsync();
                return ServiceResult<IEnumerable<EmployeeDto>>.Success(employees);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<EmployeeDto>>.Failure($"خطأ في استرجاع الموظفين: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult<EmployeeDto>> GetByIdAsync(int id)
        {
            try
            {
                var employee = await _unitOfWork.Employees.GetByIdAsync(id);
                if (employee == null)
                    return ServiceResult<EmployeeDto>.Failure("الموظف غير موجود");

                return ServiceResult<EmployeeDto>.Success(employee);
            }
            catch (Exception ex)
            {
                return ServiceResult<EmployeeDto>.Failure($"خطأ في استرجاع الموظف: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> CreateAsync(EmployeeDto dto)
        {
            try
            {
                var result = await _unitOfWork.Employees.CreateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("تم إنشاء الموظف بنجاح");
                else
                    return ServiceResult.Failure("فشل في إنشاء الموظف");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في إنشاء الموظف: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> UpdateAsync(EmployeeDto dto)
        {
            try
            {
                var result = await _unitOfWork.Employees.UpdateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("تم تحديث الموظف بنجاح");
                else
                    return ServiceResult.Failure("فشل في تحديث الموظف");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في تحديث الموظف: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> DeleteAsync(int id)
        {
            try
            {
                var result = await _unitOfWork.Employees.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("تم حذف الموظف بنجاح");
                else
                    return ServiceResult.Failure("فشل في حذف الموظف");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في حذف الموظف: {ex.Message}");
            }
        }
    }
} 