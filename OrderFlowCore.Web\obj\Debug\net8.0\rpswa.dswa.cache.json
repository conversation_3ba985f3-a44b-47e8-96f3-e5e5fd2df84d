{"GlobalPropertiesHash": "Bfyky2FCFFxmA0I8C+MiI7GsdzAuWb6eQqbi3zQLxLQ=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["3C5YQp3DleCFxSxhnEWqG4uqRvLNF1lSn87hpZ4uOwg=", "2JaGdqep89t76nZUoa9twZ9RjAvKU+CVrboRch4LYTo=", "/29NKJX5f73GWqvfX1EQtkvzAvnJxRxLJDN0+6Xs2RU=", "gh5+VHJQtHNhgOPD7/MUytBdVYoSkcsAt8VY7bqeBuQ=", "fzlqAk93uy75E/tt7VzsvN86t78PHatSYA9Hbz29uzM=", "zER0bCv6gwd976ocpE/yuidBMGLqaA/udiUs9haWmhU=", "/rtsmqr4fI4n1iLR14Z4mGvY4t8YIXqVw+wnH/v8YcM=", "yAl/8SjSKzJFKHycPZh6+E0uxPvX1yTLcxex1LLve+Y=", "geZeCtEDTI8qxjI/tvXwDw+yA9PqnusZlNiiBQWbbvk=", "FheH8WHi3C6qG0yApfFIxU5YbhC2RlFQz9HoUxhYd9s=", "dlU6lyPb172qpOmlqC3jf1MhZr8WXx5ejdUySMZxixg=", "B3jHPPDg+1J3VvOsRxgomia4BsAdgVi/mUTrkvuk65E=", "jw3eFqJZMMP16UBG/oJfsB9BfjofBl/U2kBV07LI5dE=", "B5MoeIwi7d9GoWC3MJJjETwC6U8XxHpC2pAya0yzhQo=", "UNGjIcNsDZ5WX/LVhYjcV+AhqARpvvfEmtG85tWLeHc=", "0Ckq/XggQ7V1S+7b9p1dySASSnzBCdzPqBRcuu1Jj6E=", "q9uAikafjHof7i5pfLg/VM9+gr64QYr40mw8Or0S3+E=", "ApAYwti60y8eH548eqRgL1hTQYAkajqfJDCEXdInWOI=", "Wvm6PQ6Kx6Ks0UszQoct4QdBWKNUyliorrZtRiaFnYA=", "SvRVukLl8Ggb8p/4HhOdgYulXCrppWpEUirIZgQOjLI=", "0Dbu4TlocDNJoMBOvYuZBtIUhqn7snBSAZ45eEtt4Do=", "UoT5bJEtpJD+PgP5dFUt70FXkWRfHQJX0MYahBpKsh0=", "u5izMnkrcwToVVXd8eyszfZIfo4NvpyiUySrGApe5ys=", "iif0hSqBA1KO/o/YD33+MGbdbIu3y9xS0oM2fmtWKZE=", "8a1W6ye2Iei8n7ne0dRksrpIWGGpn9CPG+S5CtVh4XA=", "vKJNrCfp/ZOFhbuPfnbUYwfLMSAruGiEmB1KbEbutzM=", "kQUyI6CBKpz+nDEWKMdM/Y1d/7ycVth9YYJdMHh8ERg=", "k1fVF7R9omDoTmW1W5V7+VeKXVY6f6eLONg8l9IKo6Y=", "gLYx5ux3wXy6DSeYKopxiiP7ToTGUpOJCQ15iXxWVhw=", "mGKIRIXRGP5EHAdY2m4a9fcwuaQCOtMkjVLmhUck0OY=", "pazrjjaUw/580twKHB2c9+qB+m8+t6FVSjJ5IaskJB8=", "cQJ0BQkn3nmCtbHXkrxsUqCq7TPamn6oTQYai6tFC2M=", "iWEvOZpvzInPvEe/IWoNz9mgdbBzLTrSULfZBchjLeg=", "S197ZgOGCcFn1NzSOIEyw2RkKjXl3DqlNSSMPiQLLPA=", "dxU3g78HEo0L+KAD5qExjD7Qj08vQHuVKOfv41/+EfM=", "7i+xpyhLSJoek/Cn3ow756SfroZRoTLhJXJl7Y//Y6k=", "YoJxL80EYvwadQKAPhOysXflPLXQeZNdXEUJj55d+0M=", "jhDH1VS9cc4H/wgaxHroixKhsogHslTOKSOWsWaIREo=", "F4Y4cE/CnwTwYVVkvv0Fs5SBBFnbX43PoIgb10MVBvA=", "CTtLo+ZcGTCd2BoDs8o+vJow6Z2jYZIPxhilSJutcAY=", "2J3NANGHCDeAEMkC2zC6uuw2p+qDDZ9ZpsrxYGQ8TzA=", "6bsVviVKw8GMFA/KdHrnSYOPMx7kJuRLYGbvPmE+IVo=", "VdxytdM6s1K/hFuIc+2ZACUfdaW0c5DZmeFUa+7tRNA=", "2cqeU6ozdA74NjvrzJ4wJWUt0YNqNYFIP+MsfFv+PXM=", "A5M8lW2L+tgOAYyNWf94kQWEnyt8nRmkHPuaob4+td4=", "SlA1A7kJTZZpVslMV0VMFsN6OFddPJ7hAXJ0FUfMASw=", "1L7gnOQB5yo/uyuiZKE3PJxaIbJJ+bRLMKrw2g4l5PU=", "UkZu3/oKkRk3+mrAtpr5PjvGao7F30/qxJKWhIC4Scs=", "N8nbvmdGqHCgAa/TMA+d8hFGj+dxV1N8PRi+S1q2c0M=", "ChgIDtKb855DSgYMTzplf7nfjD7mPZtmJQz/RCplEd0=", "rdQlXPdYQFOwd9um7uz5Uc4czfo7SwwjaMU81X2afzA=", "f3jcXFkGuhkhPUHeyCQXq0oj6D0n11+NaVMMJwjQrLU=", "QVib8xENzTjjGQX7Axb7Q0SBfRu6hJBMYKvKHgMSdA4=", "ypaFkjOIU7Tgf2kxZ+32ZvI6ZFinVkbcwu9bAgz3leA=", "zfgCmspxiiBKVntI3mD3KF04xvwjXZMU1TJMyK39UWo=", "ExLCysVII8k+ouASqQfx8iGMYl68tb8IlKeWQAtRWzc=", "Jdfkq1t2a7vKgAor8BtPEp18oFSCw3VumE2bJW3mPhY=", "PZChew+qndeGQBRVIKKy4pNs8wpIS7uBjBzcTPq7oR4=", "s+PNZMwKjM8HGMbiTEDN44g1ep5Vlk1bgzC9AYjxVxc=", "c26oL2lOFR/v8gHEW246+FEhm03Vc0yWkReN3+yq+wA=", "SrHfsrspVE4xV0Q+/7aZXlQmtVcwePY14APdt6ifOYU=", "UX955w38Ua7YZbFx8owzbOeQd8KKgH94QPFGHBzdmPo=", "RYJ1Xlx+0vz5URLQZ3O4QNEg22OZ08liaSM8qbO77Ck=", "26CtQhFv/87F4O/g1TnkX17UngESO3v3G/V1ZDiszlQ=", "jE0bQts9LCf9tS7FZ+YXorApJ1ONTYnRU2FCZgO1Bqc=", "08MZhxN1Y3Ttri/Fu6PmkqapPE8AhVnXhp5VmeScDjg=", "3jzBzkGpiEPUs2cXrHxsuyC/w5piUpWoHOe5TIppw7g=", "XFBrIRjbYuWia68aeyr/hJc/h4CPUlUTPTKpUCY0auA=", "1KLSnhbSr8B2hESpWnBBK8HxIMdm71yQyY6ZhArAddY=", "J/7vveAJPguX1M2z6BXQIe065CvpY6umdaBv60FWFYs=", "QkWhy9SmevGw3ZsaZQ73NW5EK91tgMw32C6ZG10wcxQ=", "ZzGsrD3t3Sda/lIZSz2BFcN4tM2mnBlkI00Zk1Otr28=", "9sAQyfgwHvNMCmn213F8UXtcz/eIFbfL/pM6AkVXo4c=", "OHdle/Xn2bN5Q1CSDmwK0PTX84THc/WkpFQ3azGnkxI=", "dZpcuX2Qez2bJrAdPkKfLEgu3rzfrNORMx344aXRnRI=", "gVkpCy2z3/rWk5brY3al9ouzgbOfEqU5TVMz2w2NyOI=", "b/COm2LgGmB+afrSbLhZTLsc2m2dLJLkUbZoob2xwMQ=", "jka7dYzSI8BqRWvq5R3fOwIToZCBv/kD2cPZleZfVRo=", "Sff+HXKJJ3mFSROtuJ7OawZ9nAGifhCdGdvCnz/ECeo=", "9yQ1bZ6viQ+vMfUFYE4nMSzex/v9d21ctU1rQ0H+62M=", "Ht/LsduOH5tBfeRXHAYcJ2x7WNkZL+CvrOdbxLztCRA=", "bMO2nihm+IQ2U0vLu8EYrYVxtAZm/NjMUGaL25iGLe0=", "a/hcF0ktCO6Zol/qDDG9VRwtZun6Uen6cpYTsdcEHEs=", "+JwLz+sFMt7AhRsZevWZq23i7Q/UXz0ppLpHwjYVHWY=", "6tN5epH7jLBf9hfip9hPqrS75h+v3XhALYVoPXilLdE=", "+AN9WiHfpg3vUtr9/nVsjQFxkUViSv/D5uWePw9v6QA=", "B1S6lU5kVkQmj2EOku9UiNYQaivUdc3Sj3UAKPnwIko=", "EGhMfmJ1fIaZp4wBt8uzKvyr4QGKcugj96rXTFa/gEE=", "shOBipwDr23GPha7z7BeWBzTuIm8zgpqdKfMQhqsjd0=", "x3WtyiFbA3OTpFA12lLEd3vykCcdxVaUeIzg/ORD5DY=", "Zh5n8Zavbl6po+rg5qDo8OnvYH2pnxIioi60RDYNNGw=", "TR0sK4RZZO68pSoDlAswl7j19HF4TqVLXvcwrbCxcaI=", "+XQOALdclRRVb6tPYrFvSW8UXO7d1c42foXhYk9PtHQ=", "vpcvrp7hPTkaAUmG/kT7zLg/EOwN7ecsav1Qcz9/4vY=", "DSOwOYBeGY4lwN55i/Y0y4YCNAuF1vmRfTTySc3++CQ=", "YRmplmy72fqfnLG1v0r6HVEUgPFEE2bYSAG3AfEsZIs=", "VkkB+yE70iXNffMPPvr6aDZfrBvrwarAJliX+CHED5w=", "liUMZBIf4Nm5nf+EuYyNn1NhCGVWk8FbMmVDl50oBPc=", "DsMjlncRNjw18kxo8QL7QsaXqE55hmCOLmhAmLzuDDk=", "spNM02M4fztSTT6NV1IfLTwuHgFpivqYZIP9En1+emE=", "oRmrybx+yfQbW3g9MZBQ/B5an+IQvZUH9aXqq0Qv3hU=", "WmyQlj36PYigUP1693tkFEK+RGrI41g01YzuTHB5GTY=", "loX9pxyxq1Y/1aMuw7QNoovLl2iub/icUw1Smyyvu8E=", "MJ0FANiB/jvbkmVbS+MIvgqR45MwA1MbE15EToR0GOk=", "Y5QNC48DM0aJyciOqwcCXvAo3qdjC5xY+tEe0v+FTow=", "mmtGAeAES3fyuPqX8NKyrmjtooTjw8A4QzSpG3KGRWk=", "hlTFwWC/9mNrQCKupJMeHCXLdc4vSkHuDBCKTnanmpA=", "HxorlqQLgsrAxuSknbEM3AP+YUgnPm9/Fce1Nn8Iyv0=", "yLUHw6u94Kd1OZEoSo0quND8sPACpwRlLT0DRj8Pvdc=", "ZPycUdWIeVw66GUl2UnrWZN4xoIJNUEaS7b4uyUCqJw=", "dAN7iRx/b4xK9G4kw7OT0WSiUorOSuMO61FvhB5xHOE=", "Thnx/+hmu8/GkICRFqaAz1R/TTzEJAkoNbNDnRaTJRo=", "lEA7yAXLLheYU4MIXMVIo31z7ZnNNLue8W+2OY/Qr34=", "ooaE5CHVq24mveDo6SKkPegS6jK652/SGZTQVcTwCWU=", "U1itcL9Jqx5P8fXyIER6tL4048V7zYtrPS2VOqNnrj0=", "i9uublVk9o55BoKkNMvQo2sMztOJzU+H7CNaBo5eQlM=", "Fs6g3OyvCwwkk82TQFDdoFOuMeWpi8Obwf3uT59nQKw=", "9uOvRyJWIHYcsMbUWii6wpNjGlVPdiXBByARg/cv3E0=", "EggkZqZmcjgoJtMGsL473xQqSfHAolr0UuYSmw0jN5g=", "iADcaz7mhqqzEPA++t/omzr7SeDpe3cdoVaxatghkOE=", "BXqJV48V+qYCLl0FeDLLBY+Itr+4/N7XkqpWVqeEMNE=", "/j+rzD0Re/rfeq1roiELNA9wpPsIoAGknAhOy29rxb8=", "onyt4IHF3bWbOiZoiWKb0o8U6SMICPP3dcQV/yGvgEk=", "/EG25R76l1bnjJrSYe54mrzlU9slmOzSgWvKGU7v5Ac=", "u33xmJRdZ9Pbi2IsC5X845jdnsHw3FoUEjoQPSdDuBo=", "M7I0lIzps6KBlv3qp8mUu+0HyhxCLNNamOAzlHN8ykg=", "vGrva9O4rb6bUbGQrjJRCWN7RtzT1PNJ88OImE/xHxY=", "znBi36cGyzXPIKLDISgpQxzMzWUYleAvWvnUGInB49Y=", "RzBKBDU3qdbXV743IrAc+W7rtoZ/FlsjwwKSXqtwryY=", "A4vRye8FmgzlFy8dk3PvetccmlTMNkaW8vEm3MEy1hk=", "TbkSJZ1vYqYN68fQxeXx0SmyThGwHS6D7dSa4Xu487c=", "KYfvq7KKIhdPbHHA9hWnyAJpojhViNzY+wdI2DNCChE=", "gbZeM/UiT5ZH/+4CgJPOfGRW2+QnGCn+XlK9Rp/G+Ts=", "qsTcJuIae3GEfEjlArXQj9RENcjMcXcxrqVoIXYAk3c=", "k8Z08U0igUMR2eTY4Ilqw+tn/5QeiNO1OvJxCIhLRBU=", "dcwuMhEcwB6ROXnMcUxsp46Us51GqOzLaXj71FTgss8=", "2U5gPi5T1wyzhv2FKwOGOglFHHGmv/TF8X3zsHqTWZA=", "BkzAi3A/5p594aq44zKvhSKFxLWdzlXdftOtVZBxCUQ=", "aERNWloD7DmS+VoXZ8qDsyyfoZF5I7/9FjjJ2kUgO2A=", "6WeMrnIZ4LS4naW5+0oPur6cYfEjfa5uNtrWj9b11ZM=", "wBDAJZCj5zZwB5RM68sGThPhH4UbiTI0zxOYBpES2EQ=", "IozGpRW5TwIO43JHWUItQom/KB3UiZ0dvmVculESzdk=", "wqNvitCr/opwVW+UCBmQxKXMEw7L7MoVdQKJ+wmRYZk=", "CDbQOo8auYrnK4nyCUE1C7MfvQ9mZK0K8jyWycW9zUA=", "e2o88NUPdesGtdqedsHrB3vtp0rB/naRWztT6jpJMqw=", "qRLYd18xf6tguO7/LrUkx/CKFZfCJeD2GNbr13ZHK6g=", "FgGo+zgx8ATr/dgxvXVMP4A2vEnqXvi56LPu9lNmR/E=", "y7E0y0qnVlVB5tY9qE4wLbA3VgY8UKdhzsuevji5LFU=", "kaonjo3WEsbSCE/IssCWybpK9S2XMhIfJJHHUXbpr2c=", "oN2SXXM0MDju65Xjy/6Fk290PaXOE8orMCCKf4Cvidk=", "hZ6G5lGNObhDFWZbCBZ2QYOIq8kAQjYv3cJt98Twfuw=", "s5ZIUfRvcOg8JnuM7za12A+ZCMJAMZDDVsrrf7XZMi8="], "CachedAssets": {"3C5YQp3DleCFxSxhnEWqG4uqRvLNF1lSn87hpZ4uOwg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\css\\dashbord\\dashbord.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "css/dashbord/dashbord#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "eh29qvljtq", "Integrity": "LkXllhfe/li+sITGiVMlGdr3ysvgWoRhu7HIAKmljZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\dashbord\\dashbord.css", "FileLength": 37614, "LastWriteTime": "2025-07-19T14:36:51.6102943+00:00"}, "2JaGdqep89t76nZUoa9twZ9RjAvKU+CVrboRch4LYTo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\css\\MainSite.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "css/MainSite#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cddbajxg2c", "Integrity": "cJ6pVLLZ8rMmm7taB027+uZ6xYW7HGPm3xxv24ijNYc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\MainSite.css", "FileLength": 39183, "LastWriteTime": "2025-07-19T13:10:51.0982377+00:00"}, "/29NKJX5f73GWqvfX1EQtkvzAvnJxRxLJDN0+6Xs2RU=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\favicon.ico", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3djvn4e135", "Integrity": "OWWJaDDMsSyzwiAq56NrhqM61v7tYmOC+0cwr+SLErs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 4022, "LastWriteTime": "2025-06-27T14:07:54.218245+00:00"}, "gh5+VHJQtHNhgOPD7/MUytBdVYoSkcsAt8VY7bqeBuQ=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\Breada1.png", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/Breada1#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r49qvtps7x", "Integrity": "u1NEt5uDu69/fXrFWrdVmBllySF4rOE3Ldb+p+eWiZk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\Breada1.png", "FileLength": 126397, "LastWriteTime": "2025-06-27T13:58:30.4347512+00:00"}, "fzlqAk93uy75E/tt7VzsvN86t78PHatSYA9Hbz29uzM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\Breada2.jpg", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/Breada2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f7qy3nga8b", "Integrity": "K9g9PlESiv0l4+2HEY5V+j/loHJxZYH6dH9BrcPOZp4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\Breada2.jpg", "FileLength": 142247, "LastWriteTime": "2025-05-14T15:45:14+00:00"}, "zER0bCv6gwd976ocpE/yuidBMGLqaA/udiUs9haWmhU=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\Breada3.jpg", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/Breada3#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3cvxl0parf", "Integrity": "HZ6PZANISBLVgsQdUMLQDL38HDroVuGIh2slbMNeG+k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\Breada3.jpg", "FileLength": 114703, "LastWriteTime": "2025-05-14T15:55:54+00:00"}, "/rtsmqr4fI4n1iLR14Z4mGvY4t8YIXqVw+wnH/v8YcM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\Breada4.png", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/Breada4#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "avx9uya0f1", "Integrity": "SqYkqYyQHiqHWhROGoQY3riTbwLou2n3CWrxzS90dcE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\Breada4.png", "FileLength": 319753, "LastWriteTime": "2025-05-14T15:58:29+00:00"}, "yAl/8SjSKzJFKHycPZh6+E0uxPvX1yTLcxex1LLve+Y=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\CardImage.jpg", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/CardImage#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "br1zpi7ud7", "Integrity": "pyR35oUyROVrLrOWWIOr/pS02JeYXmHud2TxL8b4w5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\CardImage.jpg", "FileLength": 13633, "LastWriteTime": "2025-05-13T20:35:24+00:00"}, "geZeCtEDTI8qxjI/tvXwDw+yA9PqnusZlNiiBQWbbvk=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\draw2.webp", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/draw2#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lb4spfgfnj", "Integrity": "4Rl+mf1UpaYn967Lz2UuVFzqBJdYMRAJAUtOw41/SGA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\draw2.webp", "FileLength": 22372, "LastWriteTime": "2025-04-27T21:45:33+00:00"}, "FheH8WHi3C6qG0yApfFIxU5YbhC2RlFQz9HoUxhYd9s=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\hero-svg-illustration.svg", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/hero-svg-illustration#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rhdii9jgqu", "Integrity": "MaXR7x3vz5T5vVgpYi7392hhJIv/lh9PzodJNCTt2K4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\hero-svg-illustration.svg", "FileLength": 6761, "LastWriteTime": "2025-06-27T14:04:00.5096616+00:00"}, "dlU6lyPb172qpOmlqC3jf1MhZr8WXx5ejdUySMZxixg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\star.png", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/star#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vbvgifvvkn", "Integrity": "ZV+qrTvBrNy2IVwDzfLhZalZ9U5VOCpF1ql7uEQAZzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\star.png", "FileLength": 152554, "LastWriteTime": "2025-05-14T17:40:25+00:00"}, "B3jHPPDg+1J3VvOsRxgomia4BsAdgVi/mUTrkvuk65E=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\.eslintrc.json", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/.eslintrc#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "uu9sszpxz5", "Integrity": "5vi79dz2bVp7aGHRPP89A633hSv0mO36l6DWib2vysQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\.eslintrc.json", "FileLength": 1228, "LastWriteTime": "2025-05-03T13:16:51+00:00"}, "jw3eFqJZMMP16UBG/oJfsB9BfjofBl/U2kBV07LI5dE=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\assistantManager.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/assistant<PERSON>anager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5g2oc4qwsv", "Integrity": "myF5P+BB+BrbjDcmgslyIyeXYWgu4J1/7R/2+KaFoyI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\assistantManager.js", "FileLength": 6491, "LastWriteTime": "2025-07-06T21:47:26.9973422+00:00"}, "B5MoeIwi7d9GoWC3MJJjETwC6U8XxHpC2pAya0yzhQo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\dashboard-charts.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/dashboard-charts#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2dw3qkz4nn", "Integrity": "liBNr2PGnkBsEQ2rztX5tUvoJp9HUg/bWdIaV5PALT8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\dashboard-charts.js", "FileLength": 9104, "LastWriteTime": "2025-07-05T10:48:11.9035287+00:00"}, "UNGjIcNsDZ5WX/LVhYjcV+AhqARpvvfEmtG85tWLeHc=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\dashboard.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/dashboard#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sngigb5vex", "Integrity": "KK8M/xNJyMD/wtulTP5DfP2q5FT/KLR3naSk1/cX1y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\dashboard.js", "FileLength": 4031, "LastWriteTime": "2025-07-19T07:27:21.4071264+00:00"}, "0Ckq/XggQ7V1S+7b9p1dySASSnzBCdzPqBRcuu1Jj6E=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\directManager.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/directManager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4qxiu8h7sg", "Integrity": "xLbMwU1vPa7Obk5CqobhL06/wfDiNOZjsBgIZsW3oF8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\directManager.js", "FileLength": 4169, "LastWriteTime": "2025-07-05T13:44:49.4634969+00:00"}, "q9uAikafjHof7i5pfLg/VM9+gr64QYr40mw8Or0S3+E=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\hrCoordinator.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/hrCoordinator#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jn8b5bim9s", "Integrity": "cS6hrEGzDDesayKs4PSlZi8Scg5EhPNIjGCOXBnhrTo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\hrCoordinator.js", "FileLength": 29098, "LastWriteTime": "2025-07-18T20:51:19.702337+00:00"}, "ApAYwti60y8eH548eqRgL1hTQYAkajqfJDCEXdInWOI=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\hrmanager.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/hrmanager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fd87yhmbu5", "Integrity": "I4zL4To50+kmEFbxvv4Y7MiEjIh+U+TnNZcscE2vAZk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\hrmanager.js", "FileLength": 21386, "LastWriteTime": "2025-07-19T09:07:29.8190283+00:00"}, "Wvm6PQ6Kx6Ks0UszQoct4QdBWKNUyliorrZtRiaFnYA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\Notification.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/Notification#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cszcmsjyt3", "Integrity": "1EnteHesam+c0VBUafqQyR9M8NmV5MZA6Cf4ze/WbEo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\Notification.js", "FileLength": 4141, "LastWriteTime": "2025-05-12T00:24:40+00:00"}, "SvRVukLl8Ggb8p/4HhOdgYulXCrppWpEUirIZgQOjLI=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\orderDetailsModule.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/orderDetailsModule#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ssubvs0ei6", "Integrity": "rF+uHQtBAapevpqSy3gRXIXb8oC5OubifI4VXJ6VvxE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\orderDetailsModule.js", "FileLength": 11846, "LastWriteTime": "2025-07-24T06:51:49.3540949+00:00"}, "0Dbu4TlocDNJoMBOvYuZBtIUhqn7snBSAZ45eEtt4Do=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\OrderDetals.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/OrderDetals#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gzu5j5ybht", "Integrity": "5MwtiTZJSCPrz4Bmcvu9CDZCVGf+rPsiIihe1lk5Pyw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\OrderDetals.js", "FileLength": 11661, "LastWriteTime": "2025-07-15T19:41:24.3687229+00:00"}, "UoT5bJEtpJD+PgP5dFUt70FXkWRfHQJX0MYahBpKsh0=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\orderStatusNotifications.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/orderStatusNotifications#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "39xzyaekzs", "Integrity": "6cIReoeQvX2cnXy/VvWoUup3DdCJ3Q0Pl705IXyizm0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\orderStatusNotifications.js", "FileLength": 16164, "LastWriteTime": "2025-07-12T14:34:00.5576518+00:00"}, "u5izMnkrcwToVVXd8eyszfZIfo4NvpyiUySrGApe5ys=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\pathManagement.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/pathManagement#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4auzfdj0yu", "Integrity": "DHdskasTgFAvd5mCSNulBaEaBDVHNbI7jJuMnB5WYrg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\pathManagement.js", "FileLength": 12313, "LastWriteTime": "2025-07-09T19:08:37.6821051+00:00"}, "iif0hSqBA1KO/o/YD33+MGbdbIu3y9xS0oM2fmtWKZE=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\shared-utils.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/shared-utils#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f4fyfmmsy5", "Integrity": "8ZeMds+sFtm5YUvpHLiDpGVt8LIBjQbt4RUqYVD2Vv4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\shared-utils.js", "FileLength": 9148, "LastWriteTime": "2025-07-16T17:58:38.4249696+00:00"}, "8a1W6ye2Iei8n7ne0dRksrpIWGGpn9CPG+S5CtVh4XA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\site.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-06-21T20:31:49.4819116+00:00"}, "vKJNrCfp/ZOFhbuPfnbUYwfLMSAruGiEmB1KbEbutzM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\supervisorOrders.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/supervisorOrders#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mjzr82eupg", "Integrity": "TmOyM5gURn6ZH0Aiu/YIzI9sOcp63xmNmu5a9WJKQPY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\supervisorOrders.js", "FileLength": 28907, "LastWriteTime": "2025-07-24T06:55:12.0940739+00:00"}, "kQUyI6CBKpz+nDEWKMdM/Y1d/7ycVth9YYJdMHh8ERg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-06-21T20:31:49.5934482+00:00"}, "k1fVF7R9omDoTmW1W5V7+VeKXVY6f6eLONg8l9IKo6Y=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-06-21T20:31:49.5954469+00:00"}, "gLYx5ux3wXy6DSeYKopxiiP7ToTGUpOJCQ15iXxWVhw=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-06-21T20:31:49.5964491+00:00"}, "mGKIRIXRGP5EHAdY2m4a9fcwuaQCOtMkjVLmhUck0OY=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-06-21T20:31:49.5974486+00:00"}, "pazrjjaUw/580twKHB2c9+qB+m8+t6FVSjJ5IaskJB8=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-06-21T20:31:49.598448+00:00"}, "cQJ0BQkn3nmCtbHXkrxsUqCq7TPamn6oTQYai6tFC2M=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-06-21T20:31:49.5994483+00:00"}, "iWEvOZpvzInPvEe/IWoNz9mgdbBzLTrSULfZBchjLeg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-06-21T20:31:49.6004486+00:00"}, "S197ZgOGCcFn1NzSOIEyw2RkKjXl3DqlNSSMPiQLLPA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-06-21T20:31:49.6014491+00:00"}, "dxU3g78HEo0L+KAD5qExjD7Qj08vQHuVKOfv41/+EfM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-06-21T20:31:49.6024535+00:00"}, "7i+xpyhLSJoek/Cn3ow756SfroZRoTLhJXJl7Y//Y6k=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-06-21T20:31:49.6034478+00:00"}, "YoJxL80EYvwadQKAPhOysXflPLXQeZNdXEUJj55d+0M=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-06-21T20:31:49.6044485+00:00"}, "jhDH1VS9cc4H/wgaxHroixKhsogHslTOKSOWsWaIREo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-06-21T20:31:49.6094632+00:00"}, "F4Y4cE/CnwTwYVVkvv0Fs5SBBFnbX43PoIgb10MVBvA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-06-21T20:31:49.6094632+00:00"}, "CTtLo+ZcGTCd2BoDs8o+vJow6Z2jYZIPxhilSJutcAY=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-06-21T20:31:49.6114511+00:00"}, "2J3NANGHCDeAEMkC2zC6uuw2p+qDDZ9ZpsrxYGQ8TzA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-06-21T20:31:49.6124469+00:00"}, "6bsVviVKw8GMFA/KdHrnSYOPMx7kJuRLYGbvPmE+IVo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-06-21T20:31:49.6134473+00:00"}, "VdxytdM6s1K/hFuIc+2ZACUfdaW0c5DZmeFUa+7tRNA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-06-21T20:31:49.6144477+00:00"}, "2cqeU6ozdA74NjvrzJ4wJWUt0YNqNYFIP+MsfFv+PXM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-06-21T20:31:49.615448+00:00"}, "A5M8lW2L+tgOAYyNWf94kQWEnyt8nRmkHPuaob4+td4=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-06-21T20:31:49.6164517+00:00"}, "SlA1A7kJTZZpVslMV0VMFsN6OFddPJ7hAXJ0FUfMASw=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-06-21T20:31:49.617448+00:00"}, "1L7gnOQB5yo/uyuiZKE3PJxaIbJJ+bRLMKrw2g4l5PU=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-06-21T20:31:49.6184482+00:00"}, "UkZu3/oKkRk3+mrAtpr5PjvGao7F30/qxJKWhIC4Scs=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-06-21T20:31:49.6204475+00:00"}, "N8nbvmdGqHCgAa/TMA+d8hFGj+dxV1N8PRi+S1q2c0M=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-06-21T20:31:49.6214487+00:00"}, "ChgIDtKb855DSgYMTzplf7nfjD7mPZtmJQz/RCplEd0=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-06-21T20:31:49.622449+00:00"}, "rdQlXPdYQFOwd9um7uz5Uc4czfo7SwwjaMU81X2afzA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-06-21T20:31:49.6254527+00:00"}, "f3jcXFkGuhkhPUHeyCQXq0oj6D0n11+NaVMMJwjQrLU=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-06-21T20:31:49.6624571+00:00"}, "QVib8xENzTjjGQX7Axb7Q0SBfRu6hJBMYKvKHgMSdA4=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-06-21T20:31:49.6644575+00:00"}, "ypaFkjOIU7Tgf2kxZ+32ZvI6ZFinVkbcwu9bAgz3leA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-06-21T20:31:49.6674546+00:00"}, "zfgCmspxiiBKVntI3mD3KF04xvwjXZMU1TJMyK39UWo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-06-21T20:31:49.6689666+00:00"}, "ExLCysVII8k+ouASqQfx8iGMYl68tb8IlKeWQAtRWzc=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-06-21T20:31:49.6719791+00:00"}, "Jdfkq1t2a7vKgAor8BtPEp18oFSCw3VumE2bJW3mPhY=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-06-21T20:31:49.6740009+00:00"}, "PZChew+qndeGQBRVIKKy4pNs8wpIS7uBjBzcTPq7oR4=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-06-21T20:31:49.6769754+00:00"}, "s+PNZMwKjM8HGMbiTEDN44g1ep5Vlk1bgzC9AYjxVxc=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-06-21T20:31:49.6799757+00:00"}, "c26oL2lOFR/v8gHEW246+FEhm03Vc0yWkReN3+yq+wA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-06-21T20:31:49.6829778+00:00"}, "SrHfsrspVE4xV0Q+/7aZXlQmtVcwePY14APdt6ifOYU=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-06-21T20:31:49.6839761+00:00"}, "UX955w38Ua7YZbFx8owzbOeQd8KKgH94QPFGHBzdmPo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-06-21T20:31:49.6869779+00:00"}, "RYJ1Xlx+0vz5URLQZ3O4QNEg22OZ08liaSM8qbO77Ck=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-06-21T20:31:49.6879768+00:00"}, "26CtQhFv/87F4O/g1TnkX17UngESO3v3G/V1ZDiszlQ=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-06-21T20:31:49.6899812+00:00"}, "jE0bQts9LCf9tS7FZ+YXorApJ1ONTYnRU2FCZgO1Bqc=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-06-21T20:31:49.6919811+00:00"}, "08MZhxN1Y3Ttri/Fu6PmkqapPE8AhVnXhp5VmeScDjg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-06-21T20:31:49.6959787+00:00"}, "3jzBzkGpiEPUs2cXrHxsuyC/w5piUpWoHOe5TIppw7g=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-06-21T20:31:49.6979767+00:00"}, "XFBrIRjbYuWia68aeyr/hJc/h4CPUlUTPTKpUCY0auA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-06-21T20:31:49.6999777+00:00"}, "1KLSnhbSr8B2hESpWnBBK8HxIMdm71yQyY6ZhArAddY=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-06-21T20:31:49.7029791+00:00"}, "J/7vveAJPguX1M2z6BXQIe065CvpY6umdaBv60FWFYs=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-06-21T20:31:49.7049769+00:00"}, "QkWhy9SmevGw3ZsaZQ73NW5EK91tgMw32C6ZG10wcxQ=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-06-21T20:31:49.7509757+00:00"}, "ZzGsrD3t3Sda/lIZSz2BFcN4tM2mnBlkI00Zk1Otr28=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-06-21T20:31:49.7925112+00:00"}, "9sAQyfgwHvNMCmn213F8UXtcz/eIFbfL/pM6AkVXo4c=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-06-21T20:31:49.7935058+00:00"}, "OHdle/Xn2bN5Q1CSDmwK0PTX84THc/WkpFQ3azGnkxI=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-06-21T20:31:49.7945061+00:00"}, "dZpcuX2Qez2bJrAdPkKfLEgu3rzfrNORMx344aXRnRI=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-06-21T20:31:49.4999139+00:00"}, "gVkpCy2z3/rWk5brY3al9ouzgbOfEqU5TVMz2w2NyOI=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-06-21T20:31:49.5009148+00:00"}, "b/COm2LgGmB+afrSbLhZTLsc2m2dLJLkUbZoob2xwMQ=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-06-21T20:31:49.5019155+00:00"}, "jka7dYzSI8BqRWvq5R3fOwIToZCBv/kD2cPZleZfVRo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-06-21T20:31:49.5019155+00:00"}, "Sff+HXKJJ3mFSROtuJ7OawZ9nAGifhCdGdvCnz/ECeo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-06-21T20:31:49.7885044+00:00"}, "9yQ1bZ6viQ+vMfUFYE4nMSzex/v9d21ctU1rQ0H+62M=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-06-21T20:31:49.4859141+00:00"}, "Ht/LsduOH5tBfeRXHAYcJ2x7WNkZL+CvrOdbxLztCRA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-06-21T20:31:49.4929236+00:00"}, "bMO2nihm+IQ2U0vLu8EYrYVxtAZm/NjMUGaL25iGLe0=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-06-21T20:31:49.4969176+00:00"}, "a/hcF0ktCO6Zol/qDDG9VRwtZun6Uen6cpYTsdcEHEs=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-06-21T20:31:49.7559946+00:00"}, "+JwLz+sFMt7AhRsZevWZq23i7Q/UXz0ppLpHwjYVHWY=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\order_printed\\11_أحمد محمد علي_04072025.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "order_printed/11_أحمد محمد علي_04072025#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5ipweew5fc", "Integrity": "47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\order_printed\\11_أحمد محمد علي_04072025.pdf", "FileLength": 0, "LastWriteTime": "2025-07-23T20:43:45.0583632+00:00"}, "6tN5epH7jLBf9hfip9hPqrS75h+v3XhALYVoPXilLdE=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_1234567890_file1_20250704150713.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_1234567890_file1_20250704150713#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wdbk1wabyj", "Integrity": "pPcNFOHBw4Ef3gPClk6tgsGHZT+tnzHeuazerOEO3wM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_1234567890_file1_20250704150713.pdf", "FileLength": 174317, "LastWriteTime": "2025-07-04T12:07:13.879368+00:00"}, "+AN9WiHfpg3vUtr9/nVsjQFxkUViSv/D5uWePw9v6QA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_1234567890_file1_20250710195413.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_1234567890_file1_20250710195413#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9psus3l2z1", "Integrity": "M7v8Td5tSoL2OG4C2tCU6r1OHuKiiInnmu0yL2yEO6w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_1234567890_file1_20250710195413.pdf", "FileLength": 326409, "LastWriteTime": "2025-07-10T16:54:13.7093974+00:00"}, "B1S6lU5kVkQmj2EOku9UiNYQaivUdc3Sj3UAKPnwIko=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_1_bf69c15d-ff5e-41b2-bfb8-74eb832baa59_order_1_file1_20250627233043.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_1_bf69c15d-ff5e-41b2-bfb8-74eb832baa59_order_1_file1_20250627233043#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wdbk1wabyj", "Integrity": "pPcNFOHBw4Ef3gPClk6tgsGHZT+tnzHeuazerOEO3wM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_1_bf69c15d-ff5e-41b2-bfb8-74eb832baa59_order_1_file1_20250627233043.pdf", "FileLength": 174317, "LastWriteTime": "2025-06-27T20:30:43.7510684+00:00"}}, "CachedCopyCandidates": {}}