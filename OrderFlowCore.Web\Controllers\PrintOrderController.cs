using DocumentFormat.OpenXml.Drawing.Charts;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Web.ViewModels;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Web.Controllers
{
    public class PrintOrderController : Controller
    {
        private readonly IOrderPrintService _orderPrintService;
        private readonly IOrderManagementService _orderManagementService;

        public PrintOrderController(IOrderPrintService orderPrintService, IOrderManagementService orderManagementService)
        {
            _orderPrintService = orderPrintService;
            _orderManagementService = orderManagementService;
        }

        // GET: /OrderPrint/
        public async Task<ActionResult> Index(string searchTerm = "", string filter = "today")
        {
            var ordersResult = await _orderPrintService.GetPrintableOrdersAsync(searchTerm, filter);
            var model = new OrderPrintViewModel
            {
                SearchTerm = searchTerm,
                Filter = filter,
                Orders = ordersResult.Data?.Select(o => new SelectListItem
                {
                    Value = o.OrderId.ToString(),
                    Text = $"{o.OrderNumber} | {o.EmployeeName}"
                }).ToList() ?? new List<SelectListItem>(),
            };

            TempData["SuccessMessage"] = ordersResult.Message;
            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> GetOrderDetails(int orderId)
        {
            var result = await _orderManagementService.GetOrderDetailsAsync(orderId);

            return Json(new { success = result.IsSuccess, message = result.Message, data = result.Data });

        }


        [HttpPost]
        public async Task<ActionResult> Print(int orderId)
        {
            var orderResult = await _orderManagementService.GetOrderDetailsAsync(orderId);
            if (!orderResult.IsSuccess || orderResult.Data == null)
                return NotFound(orderResult.Message);

            var result = await _orderPrintService.GenerateOrderPdfAsync(orderResult.Data);
            if (!result.IsSuccess || result.Data == null)
                return NotFound(result.Message);

            string fileName = $"{orderResult.Data.Id}_{orderResult.Data.EmployeeName}_{orderResult.Data.OrderDate:ddMMyyyy}.pdf";
            var cleanedFileName = SanitizeFilename(fileName);

            return File(result.Data, "application/pdf", cleanedFileName);
        }

        private string SanitizeFilename(string filename)
        {
            
            foreach (char c in Path.GetInvalidFileNameChars())
            {
                filename = filename.Replace(c, '_');
            }
            return filename;
        }
    }
} 