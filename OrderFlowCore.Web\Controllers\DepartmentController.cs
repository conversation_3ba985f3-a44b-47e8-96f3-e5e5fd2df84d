using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Interfaces.Services;

namespace OrderFlowCore.Web.Controllers;

[Authorize]
public class DepartmentController : Controller
{
    private readonly IDepartmentService _departmentService;
    private readonly ILogger<DepartmentController> _logger;

    public DepartmentController(IDepartmentService departmentService, ILogger<DepartmentController> logger)
    {
        _departmentService = departmentService;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        var result = await _departmentService.GetAllDepartmentsAsync();
        if (!result.IsSuccess)
        {
            TempData["ErrorMessage"] = result.Message;
            return RedirectToAction("Index", "Home");
        }
        
        return View(result.Data);
    }

    public IActionResult Create()
    {
        return View(new DepartmentDto());
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(DepartmentDto department)
    {
        if (!ModelState.IsValid)
        {
            return View(department);
        }

        var result = await _departmentService.CreateDepartmentAsync(department);
        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
            return RedirectToAction(nameof(Index));
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }

        return View(department);
    }

    public async Task<IActionResult> Edit(int id)
    {
        var result = await _departmentService.GetDepartmentByIdAsync(id);
        if (!result.IsSuccess)
        {
            TempData["ErrorMessage"] = result.Message;
            return RedirectToAction(nameof(Index));
        }
        
        return View(result.Data);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(DepartmentDto department)
    {
        if (ModelState.IsValid)
        {
            var result = await _departmentService.UpdateDepartmentAsync(department);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
        }
        return View(department);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Delete(int id)
    {
        var result = await _departmentService.DeleteDepartmentAsync(id);
        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }
        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ExportToExcel()
    {
        try
        {
            var result = await _departmentService.ExportToExcelAsync();
            if (result.IsSuccess)
            {
                var fileName = $"الأقسام_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";
                return File(result.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting departments to Excel");
            TempData["ErrorMessage"] = "حدث خطأ أثناء تصدير الأقسام";
            return RedirectToAction(nameof(Index));
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ImportFromExcel()
    {
        try
        {
            var file = Request.Form.Files["excelFile"];
            if (file == null || file.Length == 0)
            {
                return Json(new { success = false, message = "يرجى اختيار ملف Excel صالح" });
            }

            // Validate file type
            if (!file.ContentType.Contains("spreadsheetml") && !file.ContentType.Contains("excel"))
            {
                return Json(new { success = false, message = "يجب أن يكون الملف بصيغة Excel (.xlsx)" });
            }

            // Validate file size (5MB limit)
            if (file.Length > 5242880)
            {
                return Json(new { success = false, message = "حجم الملف يجب أن يكون أقل من 5 ميجابايت" });
            }

            using var stream = file.OpenReadStream();
            var result = await _departmentService.ImportFromExcelAsync(stream);

            if (result.IsSuccess)
            {
                return Json(new {
                    success = true,
                    message = result.Message,
                    importedCount = result.Data,
                    errors = result.Errors
                });
            }
            else
            {
                return Json(new { success = false, message = result.Message });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing departments from Excel");
            return Json(new { success = false, message = "حدث خطأ أثناء استيراد الأقسام" });
        }
    }
}