@model OrderFlowCore.Web.ViewModels.OrderPrintViewModel
@{
    ViewBag.Title = "طباعة الطلبات المعتمدة";
}

<div class="container mt-5">
    <div class="card border-success shadow-lg">
        <div class="card-header bg-success text-white py-3 position-sticky-top">
            <h4 class="card-title mb-0 fw-bold d-flex align-items-center">
                <i class="fas fa-print me-3 fa-beat-fade"></i>
                نظام طباعة الطلبات المعتمدة
            </h4>
        </div>
        <div class="card-body p-4">
            @using (Html.BeginForm("Index", "PrintOrder", FormMethod.Get, new { @class = "row g-3 mb-4 align-items-end", id = "searchFilterForm" }))
            {
                <input type="hidden" name="filter" id="filterInput" value="@Model.Filter" />
                <div class="col-12 col-lg-8 mb-3 mb-lg-0">
                    <div class="input-group input-group-lg has-validation">
                        @Html.TextBoxFor(m => m.SearchTerm, new { @class = "form-control border-2 border-success rounded-start-4 shadow-sm", placeholder = "ابحث برقم الطلب / اسم الموظف..." })
                        <button class="input-group-text bg-success text-white rounded-end-4 shadow-sm" type="submit" title="بحث">
                            <i class="fas fa-search fa-fw"></i>
                        </button>
                    </div>
                </div>
                <div class="col-12 col-lg-4">
                    <div class="d-grid gap-2 d-lg-flex align-items-stretch">
                        <button type="button" data-filter="today" class="btn btn-outline-success px-3 filter-btn @(Model.Filter == "today" ? "active" : "")">اليوم</button>
                        <button type="button" data-filter="week" class="btn btn-outline-success px-3 filter-btn @(Model.Filter == "week" ? "active" : "")">أسبوع</button>
                        <button type="button" data-filter="month" class="btn btn-outline-success px-3 filter-btn @(Model.Filter == "month" ? "active" : "")">شهر</button>
                        <button type="button" data-filter="all" class="btn btn-outline-success px-3 filter-btn @(Model.Filter == "all" ? "active" : "")">الكل</button>
                    </div>
                </div>
                <div class="col-12 mt-3">
                    @Html.DropDownListFor(m => m.SelectedOrderId, Model.Orders, "اختر الطلب من القائمة",
                        new { @class = "form-select form-select-lg border-2 border-success rounded-3 shadow-sm", @id = "orderSelect" })
                </div>
            }
        </div>
    </div>

    <div class="text-center my-4">
        <form method="post" action="@Url.Action("Print", "PrintOrder")" class="d-inline-block">
            <input type="hidden" name="orderId" id="selectedOrderId" value="@Model.SelectedOrderId" />
            <button type="submit" class="btn btn-success btn-lg px-5 shadow-sm">
                <i class="fas fa-print me-2"></i> طباعة
            </button>
        </form>
    </div>

    <div id="messageContainer" class="mt-3"></div>

    <!-- Loading -->
    <div id="loading" class="loading text-center" style="display: none;">
        <div class="spinner-border text-success" role="status" style="width: 3rem; height: 3rem;">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-2">جاري تحميل تفاصيل الطلب...</p>
    </div>

    <!-- Order Details Section -->
    <div class="mt-4">
        @await Html.PartialAsync("_OrderDetailsPartial")
    </div>
</div>

@section Scripts {
    <script src="~/js/orderDetailsModule.js"></script>
    <script>
        $(function () {
            // Order selection change handler (Index page)
            const orderSelect = document.getElementById('orderSelect');
            if (orderSelect) {
                orderSelect.addEventListener('change', (e) => {
                    const orderId = e.target.value;
                    if (!orderId) {
                        OrderDetailsModule.hideOrderDetails();
                        return;
                    }

                    $('#selectedOrderId').val(orderId);

                    OrderDetailsModule.loadOrderDetails(orderId, '/PrintOrder/GetOrderDetails');
                });
            }

            // Filter button click handler
            $('.filter-btn').on('click', function () {
                var filter = $(this).data('filter');
                $('#filterInput').val(filter);
                $('#searchFilterForm').submit();
            });
        });
    </script>
}