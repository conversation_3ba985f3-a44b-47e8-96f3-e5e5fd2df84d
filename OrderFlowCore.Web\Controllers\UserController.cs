using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;

using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace OrderFlowCore.Web.Controllers
{
    [Authorize]
    public class UserController : Controller
    {
        private readonly IUserService _userService;
        
        private readonly ILogger<UserController> _logger;

        public UserController(IUserService userService, ILogger<UserController> logger)
        {
            _userService = userService;
            
            _logger = logger;
        }

        public async Task<IActionResult> Profile()
        {
            var userId = int.Parse(User.FindFirstValue(ClaimTypes.NameIdentifier));
            var result = await _userService.GetProfileAsync(userId);
            
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction("Index", "Dashboard");
            }
            

            var vm = UserProfileViewModel.FromDto(result.Data);
            return View(vm);
        }

        [HttpGet]
        public async Task<IActionResult> EditProfile()
        {
            var userId = int.Parse(User.FindFirstValue(ClaimTypes.NameIdentifier));
            var result = await _userService.GetProfileAsync(userId);
            
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Profile));
            }
            
            var vm = UserProfileViewModel.FromDto(result.Data);
            return View(vm);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditProfile(UserProfileViewModel vm)
        {
            if (!ModelState.IsValid)
            {
                TempData["ErrorMessage"] = "يرجى تصحيح الأخطاء في النموذج";
                return View(vm);
            }
            
            var currentUserId = int.Parse(User.FindFirstValue(ClaimTypes.NameIdentifier));
            if (vm.Id != currentUserId)
            {
                TempData["ErrorMessage"] = "غير مسموح لك بتعديل ملف شخصي آخر";
                return RedirectToAction(nameof(Profile));
            }
            
            var dto = UserProfileViewModel.ToDto(vm);
            var result = await _userService.UpdateProfileAsync(dto);
            
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return View(vm);
            }
            
            TempData["SuccessMessage"] = result.Message;
            return RedirectToAction(nameof(Profile));
        }

        [HttpGet]
        public IActionResult ChangePassword()
        {
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ChangePassword(ChangePasswordDto dto)
        {
            if (!ModelState.IsValid)
            {
                TempData["ErrorMessage"] = "يرجى تصحيح الأخطاء في النموذج";
                return View(dto);
            }
            
            var userId = int.Parse(User.FindFirstValue(ClaimTypes.NameIdentifier));
            var result = await _userService.ChangePasswordAsync(userId, dto);
            
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return View(dto);
            }
            
            TempData["SuccessMessage"] = result.Message;
            return RedirectToAction(nameof(Profile));
        }
    }
} 