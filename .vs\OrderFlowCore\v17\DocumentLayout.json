{"Version": 1, "WorkspaceRootPath": "E:\\Projects\\abozyad\\OrderFlowCore\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\assistantmanagercontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\assistantmanagercontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{DDC2C32B-0409-4942-94F5-65603C464384}|OrderFlowCore.Core\\OrderFlowCore.Core.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.core\\entities\\assistantmanagertype.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{DDC2C32B-0409-4942-94F5-65603C464384}|OrderFlowCore.Core\\OrderFlowCore.Core.csproj|solutionrelative:orderflowcore.core\\entities\\assistantmanagertype.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\accountmanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\accountmanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\interfaces\\services\\iuserservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\interfaces\\services\\iuserservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\userservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\userservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\accountscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\accountscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\authservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\authservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\dashboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\dashboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\roleservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\roleservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\dtos\\userprofiledto.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\dtos\\userprofiledto.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\user\\editprofile.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\user\\editprofile.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\viewmodels\\userprofileviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\viewmodels\\userprofileviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\shared\\_sidebarpartial.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\shared\\_sidebarpartial.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\dashboard\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\dashboard\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\views\\accounts\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\views\\accounts\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\ordersmanagementcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\ordersmanagementcontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{DDC2C32B-0409-4942-94F5-65603C464384}|OrderFlowCore.Core\\OrderFlowCore.Core.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.core\\entities\\userrole.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{DDC2C32B-0409-4942-94F5-65603C464384}|OrderFlowCore.Core\\OrderFlowCore.Core.csproj|solutionrelative:orderflowcore.core\\entities\\userrole.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\supervisororderservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\supervisororderservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\orderrestorationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\orderrestorationservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.application\\services\\supervisorservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{F10499D8-9057-407E-B19F-023A66BDC08F}|OrderFlowCore.Application\\OrderFlowCore.Application.csproj|solutionrelative:orderflowcore.application\\services\\supervisorservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|e:\\projects\\abozyad\\orderflowcore\\orderflowcore.web\\controllers\\supervisororderscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{64E0F358-387F-448D-B07D-CEE4647637BE}|OrderFlowCore.Web\\OrderFlowCore.Web.csproj|solutionrelative:orderflowcore.web\\controllers\\supervisororderscontroller.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 199, "SelectedChildIndex": 5, "Children": [{"$type": "Document", "DocumentIndex": 4, "Title": "UserService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\UserService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\UserService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\UserService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\UserService.cs", "ViewState": "AgIAAI8AAAAAAAAAAAAswJ8AAABJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T10:37:35.521Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "IUserService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Interfaces\\Services\\IUserService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Interfaces\\Services\\IUserService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Interfaces\\Services\\IUserService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Interfaces\\Services\\IUserService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwBQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T10:36:58.996Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 5, "Title": "AccountsController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AccountsController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\AccountsController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AccountsController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\AccountsController.cs", "ViewState": "AgIAABAAAAAAAAAAAAAgwCgAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T09:19:07.785Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "AssistantManagerType.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\Entities\\AssistantManagerType.cs", "RelativeDocumentMoniker": "OrderFlowCore.Core\\Entities\\AssistantManagerType.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\Entities\\AssistantManagerType.cs", "RelativeToolTip": "OrderFlowCore.Core\\Entities\\AssistantManagerType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:54:12.735Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "AssistantManagerController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\AssistantManagerController.cs", "ViewState": "AgIAAHkAAAAAAAAAAAArwCMAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:51:52.703Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "UserProfileViewModel.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\ViewModels\\UserProfileViewModel.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\ViewModels\\UserProfileViewModel.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\ViewModels\\UserProfileViewModel.cs", "RelativeToolTip": "OrderFlowCore.Web\\ViewModels\\UserProfileViewModel.cs", "ViewState": "AgIAACIAAAAAAAAAAAAuwD8AAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:44:02.145Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "UserProfileDto.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\DTOs\\UserProfileDto.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\DTOs\\UserProfileDto.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\DTOs\\UserProfileDto.cs", "RelativeToolTip": "OrderFlowCore.Application\\DTOs\\UserProfileDto.cs", "ViewState": "AgIAAAcAAAAAAAAAAIAxwBQAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:43:22.379Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "UserController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\UserController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\UserController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\UserController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\UserController.cs", "ViewState": "AgIAACwAAAAAAAAAAAAwwDcAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:40:46.853Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "EditProfile.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\User\\EditProfile.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\User\\EditProfile.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\User\\EditProfile.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\User\\EditProfile.cshtml", "ViewState": "AgIAAGYAAAAAAAAAAAAAAIIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-24T08:40:13.644Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "_SidebarPartial.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\Shared\\_SidebarPartial.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\Shared\\_SidebarPartial.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\Shared\\_SidebarPartial.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\Shared\\_SidebarPartial.cshtml", "ViewState": "AgIAABgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-24T08:39:42.515Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "Index.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\Dashboard\\Index.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\Dashboard\\Index.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\Dashboard\\Index.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\Dashboard\\Index.cshtml", "ViewState": "AgIAAAYAAAAAAAAAAAAAABcAAABWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-24T08:38:34.056Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "Index.cshtml", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\Accounts\\Index.cshtml", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Views\\Accounts\\Index.cshtml", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Views\\Accounts\\Index.cshtml", "RelativeToolTip": "OrderFlowCore.Web\\Views\\Accounts\\Index.cshtml", "ViewState": "AgIAADAAAAAAAAAAAAAAAEIAAABkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-24T08:38:05.688Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "UserRole.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\Entities\\UserRole.cs", "RelativeDocumentMoniker": "OrderFlowCore.Core\\Entities\\UserRole.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Core\\Entities\\UserRole.cs", "RelativeToolTip": "OrderFlowCore.Core\\Entities\\UserRole.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAABcAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:15:45.272Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "AuthController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\AuthController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\AuthController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\AuthController.cs", "ViewState": "AgIAACoAAAAAAAAAAAAqwD4AAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:12:10.193Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AccountManagementService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\AccountManagementService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\AccountManagementService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\AccountManagementService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\AccountManagementService.cs", "ViewState": "AgIAAJoAAAAAAAAAAAApwDQAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:11:47.104Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "AuthService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\AuthService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\AuthService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\AuthService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\AuthService.cs", "ViewState": "AgIAAB0AAAAAAAAAAAAqwC4AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:11:42.531Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "RoleService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\RoleService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\RoleService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\RoleService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\RoleService.cs", "ViewState": "AgIAAHMAAAAAAAAAAAAYwF4AAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:10:54.291Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "OrdersManagementController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\OrdersManagementController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\OrdersManagementController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\OrdersManagementController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\OrdersManagementController.cs", "ViewState": "AgIAAAABAAAAAAAAAAAjwB4BAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:10:03.173Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "DashboardController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\DashboardController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\DashboardController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\DashboardController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\DashboardController.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAQwCAAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:09:23.536Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "SupervisorOrderService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\SupervisorOrderService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\SupervisorOrderService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\SupervisorOrderService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\SupervisorOrderService.cs", "ViewState": "AgIAAKoAAAAAAAAAAAAvwMUAAABLAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T07:58:54.021Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "OrderRestorationService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\OrderRestorationService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\OrderRestorationService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\OrderRestorationService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\OrderRestorationService.cs", "ViewState": "AgIAAFkAAAAAAAAAAAAcwG8AAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T07:56:50.729Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "SupervisorService.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\SupervisorService.cs", "RelativeDocumentMoniker": "OrderFlowCore.Application\\Services\\SupervisorService.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Application\\Services\\SupervisorService.cs", "RelativeToolTip": "OrderFlowCore.Application\\Services\\SupervisorService.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAWwCEAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T07:43:38.158Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "SupervisorOrdersController.cs", "DocumentMoniker": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "RelativeDocumentMoniker": "OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "ToolTip": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "RelativeToolTip": "OrderFlowCore.Web\\Controllers\\SupervisorOrdersController.cs", "ViewState": "AgIAAHsAAAAAAAAAAAAuwJ4AAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T06:22:57.697Z", "EditorCaption": ""}]}]}]}