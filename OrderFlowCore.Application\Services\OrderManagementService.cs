using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Exceptions;
using OrderFlowCore.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    
    public class OrderManagementService : IOrderManagementService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly OrderServiceOptions _options;
        private readonly IFileService _fileService;
        private readonly ILogger<OrderManagementService> _logger;

        public OrderManagementService(
            IUnitOfWork unitOfWork,
            IOptions<OrderServiceOptions> options,
            IFileService fileService,
            ILogger<OrderManagementService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _fileService = fileService ?? throw new ArgumentNullException(nameof(fileService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ServiceResult<DropdownDataDto>> GetDropdownDataAsync()
        {
            try
            {
                var dropdownData = new DropdownDataDto();

                // Load data from repositories with error handling
                var departments = await _unitOfWork.Departments.GetAllAsync();
                var orderTypes = await _unitOfWork.OrdersTypes.GetAllAsync();
                var jobTypes = await _unitOfWork.JobTypes.GetAllAsync();
                var nationalities = await _unitOfWork.Nationalities.GetAllAsync();
                var employmentTypes = await _unitOfWork.EmploymentTypes.GetAllAsync();
                var qualifications = await _unitOfWork.Qualifications.GetAllAsync();

                // Convert data to DropdownItemDto
                dropdownData.Departments = departments.Select(d => new DropdownItemDto { Value = d.Name, Text = d.Name }).ToList();
                dropdownData.OrderTypes = orderTypes.Select(ot => new DropdownItemDto { Value = ot.Name, Text = ot.Name }).ToList();
                dropdownData.JobTitles = jobTypes.Select(jt => new DropdownItemDto { Value = jt.Name, Text = jt.Name }).ToList();
                dropdownData.Nationalities = nationalities.Select(n => new DropdownItemDto { Value = n.Name, Text = n.Name }).ToList();
                dropdownData.EmploymentTypes = employmentTypes.Select(et => new DropdownItemDto { Value = et.Name, Text = et.Name }).ToList();
                dropdownData.Qualifications = qualifications.Select(q => new DropdownItemDto { Value = q.Name, Text = q.Name }).ToList();

                return ServiceResult<DropdownDataDto>.Success(dropdownData);
            }
            catch (Exception ex)
            {
                return ServiceResult<DropdownDataDto>.Failure($"خطأ في تحميل بيانات القوائم المنسدلة: {ex.Message}");
            }
        }

        public async Task<ServiceResult<OrderSummaryDto>> CreateOrderAsync(OrderNewDto orderDto)
        {
            try
            {
                // Validate input
                var validationResult = ValidateOrderDto(orderDto);
                if (!validationResult.IsSuccess)
                {
                    return ServiceResult<OrderSummaryDto>.Failure(validationResult.Message, validationResult.Errors);
                }

                var order = new OrdersTable
                {
                    EmployeeName = orderDto.EmployeeName.Trim(),
                    JobTitle = orderDto.JobTitle.Trim(),
                    EmployeeNumber = orderDto.EmployeeNumber.Trim(),
                    CivilRecord = orderDto.CivilRecord.Trim(),
                    Nationality = orderDto.Nationality.Trim(),
                    MobileNumber = orderDto.MobileNumber.Trim(),
                    Department = orderDto.Department.Trim(),
                    EmploymentType = orderDto.EmploymentType.Trim(),
                    Qualification = orderDto.Qualification.Trim(),
                    OrderType = orderDto.OrderType.Trim(),
                    Details = orderDto.Details?.Trim() ?? string.Empty,
                    CreatedAt = DateTime.UtcNow,
                    OrderStatus = OrderStatus.DM,
                };

                // Use FileService for attachments
                var attachmentResult = await _fileService.UploadFilesAsync(orderDto.Attachments, $"order_{orderDto.CivilRecord}");
                if (!attachmentResult.IsSuccess)
                {
                    return ServiceResult<OrderSummaryDto>.Failure(attachmentResult.Message);
                }

                for (int i = 0; i < attachmentResult.Data.Count && i < _options.MaxAttachments; i++)
                {
                    var url = attachmentResult.Data[i];
                    switch (i)
                    {
                        case 0: order.File1Url = url; break;
                        case 1: order.File2Url = url; break;
                        case 2: order.File3Url = url; break;
                        case 3: order.File4Url = url; break;
                    }
                }

                await _unitOfWork.Orders.AddAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult<OrderSummaryDto>.Success(new OrderSummaryDto
                {
                    Id = order.Id,
                    EmployeeName = order.EmployeeName,
                    OrderType = order.OrderType,
                    Department = order.Department,
                    CreatedAt = order.CreatedAt,
                    OrderStatus = order.OrderStatus
                }, "تم إنشاء الطلب بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<OrderSummaryDto>.Failure($"خطأ في إنشاء الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult<OrderDetailsDto>> GetOrderDetailsAsync(int orderId)
        {
            try
            {
                _logger.LogInformation("Getting order details for order ID: {OrderId}", orderId);

                if (orderId <= 0)
                {
                    _logger.LogWarning("Invalid order ID provided: {OrderId}", orderId);
                    throw new ValidationException("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    _logger.LogWarning("Order not found: {OrderId}", orderId);
                    throw new OrderNotFoundException(orderId);
                }

                var orderDetails = OrderDetailsDto.FromDomain(order);

                _logger.LogInformation("Successfully retrieved order details for order ID: {OrderId}", orderId);
                return ServiceResult<OrderDetailsDto>.Success(orderDetails);
            }
            catch (BusinessLogicException)
            {
                throw; // Re-throw business logic exceptions
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order details for order ID: {OrderId}", orderId);
                return ServiceResult<OrderDetailsDto>.Failure($"خطأ في جلب تفاصيل الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult<string>> GetOrderSpecialActionAsync(int orderId, string? supervisorId)
        {
            try
            {
                _logger.LogInformation("Getting order details for order ID: {OrderId}", orderId);

                if (orderId <= 0)
                {
                    _logger.LogWarning("Invalid order ID provided: {OrderId}", orderId);
                    throw new ValidationException("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    _logger.LogWarning("Order not found: {OrderId}", orderId);
                    throw new OrderNotFoundException(orderId);
                }

                var specialAction = await _unitOfWork.SupervisorsFollowUps.GetAsync(supervisorId,order.CivilRecord);
                if (specialAction == null)
                {
                    return ServiceResult<string>.Failure("لا توجد ملاحظات خاصة من المشرف");
                }

                _logger.LogInformation("Successfully retrieved order special action for order ID: {OrderId}", orderId);
                return ServiceResult<string>.Success(specialAction.SpecialProcedure);
            }
            catch (BusinessLogicException)
            {
                throw; // Re-throw business logic exceptions
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order details for order ID: {OrderId}", orderId);
                return ServiceResult<string>.Failure($"خطأ في جلب تفاصيل الطلب: {ex.Message}");
            }
        }
        public async Task<ServiceResult<OrderDetailsDto>> GetOrderDetailsByIdAndCivilRecordAsync(int orderId, string civilRecord)
        {
            try
            {
                if (orderId <= 0 || string.IsNullOrWhiteSpace(civilRecord))
                {
                    return ServiceResult<OrderDetailsDto>.Failure("رقم الطلب أو السجل المدني غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null || !order.CivilRecord.Equals(civilRecord.Trim(), StringComparison.OrdinalIgnoreCase))
                {
                    return ServiceResult<OrderDetailsDto>.Failure("رقم الطلب أو السجل المدني غير صحيح");
                }

                var orderDetails = OrderDetailsDto.FromDomain(order);
                return ServiceResult<OrderDetailsDto>.Success(orderDetails);
            }
            catch (Exception ex)
            {
                return ServiceResult<OrderDetailsDto>.Failure($"خطأ في جلب تفاصيل الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> UploadAttachmentAsync(int orderId, byte[] fileData, string fileName, int fileNumber)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (fileData == null || fileData.Length == 0)
                {
                    return ServiceResult.Failure("بيانات الملف فارغة");
                }

                if (string.IsNullOrWhiteSpace(fileName))
                {
                    return ServiceResult.Failure("اسم الملف مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Determine which file slot to update and delete old file if exists
                string oldFileUrl = null;
                switch (fileNumber)
                {
                    case 1:
                        oldFileUrl = order.File1Url;
                        break;
                    case 2:
                        oldFileUrl = order.File2Url;
                        break;
                    case 3:
                        oldFileUrl = order.File3Url;
                        break;
                    case 4:
                        oldFileUrl = order.File4Url;
                        break;
                    default:
                        return ServiceResult.Failure("رقم المرفق غير صحيح");
                }

                if (!string.IsNullOrEmpty(oldFileUrl))
                {
                    await _fileService.DeleteFileAsync(oldFileUrl);
                }

                // Upload new file
                var uploadResult = await _fileService.UploadFileAsync(fileData, fileName, $"order_{order.CivilRecord}");
                if (!uploadResult.IsSuccess)
                {
                    return ServiceResult.Failure(uploadResult.Message);
                }
                var fileUrl = uploadResult.Data;

                // Update the correct file URL property
                switch (fileNumber)
                {
                    case 1:
                        order.File1Url = fileUrl;
                        break;
                    case 2:
                        order.File2Url = fileUrl;
                        break;
                    case 3:
                        order.File3Url = fileUrl;
                        break;
                    case 4:
                        order.File4Url = fileUrl;
                        break;
                }

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم رفع المرفق بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في رفع المرفق: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> DownloadAttachmentsZipAsync(int orderId)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult<byte[]>.Failure("رقم الطلب غير صحيح");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<byte[]>.Failure("لم يتم العثور على الطلب");
                }

                var fileUrls = new List<string>();
                if (!string.IsNullOrEmpty(order.File1Url)) fileUrls.Add(order.File1Url);
                if (!string.IsNullOrEmpty(order.File2Url)) fileUrls.Add(order.File2Url);
                if (!string.IsNullOrEmpty(order.File3Url)) fileUrls.Add(order.File3Url);
                if (!string.IsNullOrEmpty(order.File4Url)) fileUrls.Add(order.File4Url);

                if (fileUrls.Count == 0)
                {
                    return ServiceResult<byte[]>.Failure("لا توجد مرفقات");
                }

                // Use FileService for zipping
                var zipResult = await _fileService.DownloadFilesZipAsync(fileUrls);
                if (!zipResult.IsSuccess)
                {
                    return ServiceResult<byte[]>.Failure(zipResult.Message);
                }

                return ServiceResult<byte[]>.Success(zipResult.Data);
            }
            catch (Exception ex)
            {
                return ServiceResult<byte[]>.Failure($"خطأ في تحميل المرفقات: {ex.Message}");
            }
        }

        #region Private Helper Methods

        private ServiceResult ValidateOrderDto(OrderNewDto orderDto)
        {
            var errors = new List<string>();

            if (orderDto == null)
            {
                return ServiceResult.Failure("بيانات الطلب مطلوبة");
            }

            // Validate required fields
            if (string.IsNullOrWhiteSpace(orderDto.EmployeeName))
                errors.Add("اسم الموظف مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.JobTitle))
                errors.Add("الوظيفة مطلوبة");

            if (string.IsNullOrWhiteSpace(orderDto.EmployeeNumber))
                errors.Add("رقم الموظف مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.CivilRecord))
                errors.Add("السجل المدني مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.Nationality))
                errors.Add("الجنسية مطلوبة");

            if (string.IsNullOrWhiteSpace(orderDto.MobileNumber))
                errors.Add("رقم الجوال مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.Department))
                errors.Add("القسم مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.EmploymentType))
                errors.Add("نوع التوظيف مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.Qualification))
                errors.Add("المؤهل مطلوب");

            if (string.IsNullOrWhiteSpace(orderDto.OrderType))
                errors.Add("نوع الطلب مطلوب");

            // Validate mobile number format
            if (!string.IsNullOrWhiteSpace(orderDto.MobileNumber) &&
                !Regex.IsMatch(orderDto.MobileNumber, @"^[0-9+\-\s()]+$"))
            {
                errors.Add("صيغة رقم الجوال غير صحيحة");
            }

            // Validate attachments count
            if (orderDto.Attachments?.Count > _options.MaxAttachments)
            {
                errors.Add($"الحد الأقصى للمرفقات هو {_options.MaxAttachments}");
            }

            return errors.Any()
                ? ServiceResult.Failure("فشل في التحقق من صحة البيانات", errors)
                : ServiceResult.Success();
        }

        #endregion
    }
}
