namespace OrderFlowCore.Core.Entities
{
    public enum AssistantManagerType
    {
        A1, // مساعد المدير للخدمات الطبية
        A2, // مساعد المدير لخدمات التمريض
        A3, // مساعد المدير للخدمات الإدارية والتشغيل
        A4, // مساعد المدير للموارد البشرية
        B,  // منسق الموارد البشرية
        Unknown // غير محدد
    }

    public static class AssistantManagerTypeExtensions
    {
        public static string ToDisplayName(this AssistantManagerType type)
        {
            return type switch
            {
                AssistantManagerType.A1 => "مساعد المدير للخدمات الطبية",
                AssistantManagerType.A2 => "مساعد المدير لخدمات التمريض",
                AssistantManagerType.A3 => "مساعد المدير للخدمات الإدارية والتشغيل",
                AssistantManagerType.A4 => "مساعد المدير للموارد البشرية",
                AssistantManagerType.B => "منسق الموارد البشرية",
                _ => "غير محدد"
            };
        }
        public static string ToId(this AssistantManagerType type)
        {
            return type switch
            {
                AssistantManagerType.A1 => "A1",
                AssistantManagerType.A2 => "A2",
                AssistantManagerType.A3 => "A3",
                AssistantManagerType.A4 => "A4",
                AssistantManagerType.B => "B",
                _ => "غير محدد"
            };
        }
        public static AssistantManagerType FromDisplayString(string? name)
        {
            return name switch
            {
                "مساعد المدير للخدمات الطبية" => AssistantManagerType.A1,
                "مساعد المدير لخدمات التمريض" => AssistantManagerType.A2,
                "مساعد المدير للخدمات الإدارية والتشغيل" => AssistantManagerType.A3,
                "مساعد المدير للموارد البشرية" => AssistantManagerType.A4,
                "منسق الموارد البشرية" => AssistantManagerType.B,
                _ => AssistantManagerType.Unknown
            };
        }

        public static OrderStatus ToOrderStatus(this AssistantManagerType type)
        {
            return type switch
            {
                AssistantManagerType.A1 => OrderStatus.A1,
                AssistantManagerType.A2 => OrderStatus.A2,
                AssistantManagerType.A3 => OrderStatus.A3,
                AssistantManagerType.A4 => OrderStatus.A4,
                AssistantManagerType.B => OrderStatus.B,
                _ => OrderStatus.A1
            };
        }
    }
} 