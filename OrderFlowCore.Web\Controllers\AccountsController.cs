using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Web.Controllers;

[Authorize]
public class AccountsController : Controller
{
    private readonly IAccountManagementService _accountManagementService;
    private readonly ILogger<AccountsController> _logger;

    public AccountsController(
        IAccountManagementService accountManagementService,
        ILogger<AccountsController> logger)
    {
        _accountManagementService = accountManagementService;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        var result = await _accountManagementService.GetAccountsDataAsync();
        if (!result.IsSuccess)
        {
            TempData["ErrorMessage"] = result.Message;
            return RedirectToAction("Index", "Dashboard");
        }

        var dto = result.Data;

        return View(dto);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> CreateUser(UserCreateDto dto)
    {
        var result = await _accountManagementService.CreateUserAsync(dto);
        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }
        return RedirectToAction("Index");
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateUser(UserEditDto dto)
    {
        var result = await _accountManagementService.UpdateUserAsync(dto);
        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }
        return RedirectToAction("Index");
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteUser(string username)
    {
        var result = await _accountManagementService.DeleteUserAsync(username);

        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }

        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SearchUsers(string searchTerm)
    {
        var result = await _accountManagementService.SearchUsersAsync(searchTerm);

        if (result.IsSuccess)
        {
            return View("Index", result.Data);
        }

        TempData["ErrorMessage"] = result.Message;
        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ChangeUserPassword(AdminChangePasswordDto dto)
    {
        var result = await _accountManagementService.ChangeUserPasswordAsync(dto);
        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }
        return RedirectToAction("Index");
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ExportToExcel()
    {
        try
        {
            var result = await _accountManagementService.ExportUsersToExcelAsync();
            if (result.IsSuccess)
            {
                var fileName = $"المستخدمين_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";
                return File(result.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting users to Excel");
            TempData["ErrorMessage"] = "حدث خطأ أثناء تصدير المستخدمين";
            return RedirectToAction(nameof(Index));
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ImportFromExcel()
    {
        try
        {
            var file = Request.Form.Files["excelFile"];
            if (file == null || file.Length == 0)
            {
                return Json(new { success = false, message = "يرجى اختيار ملف Excel صالح" });
            }

            // Validate file type
            if (!file.ContentType.Contains("spreadsheetml") && !file.ContentType.Contains("excel"))
            {
                return Json(new { success = false, message = "يجب أن يكون الملف بصيغة Excel (.xlsx)" });
            }

            // Validate file size (5MB limit)
            if (file.Length > 5242880)
            {
                return Json(new { success = false, message = "حجم الملف يجب أن يكون أقل من 5 ميجابايت" });
            }

            using var stream = file.OpenReadStream();
            var result = await _accountManagementService.ImportUsersFromExcelAsync(stream);

            if (result.IsSuccess)
            {
                return Json(new {
                    success = true,
                    message = result.Message,
                    importedCount = result.Data,
                    errors = result.Errors
                });
            }
            else
            {
                return Json(new { success = false, message = result.Message });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing users from Excel");
            return Json(new { success = false, message = "حدث خطأ أثناء استيراد المستخدمين" });
        }
    }
}
