@model OrderFlowCore.Web.ViewModels.HRCoordinatorDashboardViewModel

@{
    ViewData["Title"] = "لوحة تحكم منسق الموارد البشرية";
}

<div class="container-fluid dashboard-container">
    @if (Model.HasError)
    {
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>خطأ:</strong> @Model.ErrorMessage
        </div>
    }

    <!-- Welcome Header -->
    <div class="welcome-header mb-4">
        <div class="row align-items-center">
            <div class="col-md-2 text-center">
                <i class="fas fa-user-tie fa-4x text-primary"></i>
            </div>
            <div class="col-md-10">
                <h2 class="mb-1">مرحباً، @Model.Username!</h2>
                <p class="mb-1"><i class="fas fa-user-tag me-2"></i>الدور: @Model.Role</p>
                <p class="mb-0"><i class="fas fa-envelope me-2"></i>البريد الإلكتروني: @Model.Email</p>
            </div>
        </div>
    </div>

    @if (!Model.HasError)
    {
        <!-- Key Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card primary">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number">@Model.Statistics.TotalOrdersForCoordinator</div>
                            <div>إجمالي الطلبات</div>
                        </div>
                        <i class="fas fa-file-alt stat-icon"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card warning">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number">@Model.Statistics.PendingOrdersForCoordinator</div>
                            <div>الطلبات المعلقة</div>
                        </div>
                        <i class="fas fa-clock stat-icon"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card success">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number">@Model.Statistics.OrdersAtSupervisors</div>
                            <div>عند المشرفين</div>
                        </div>
                        <i class="fas fa-users stat-icon"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stat-card danger">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="stat-number">@Model.Statistics.OrdersRequiringAction</div>
                            <div>تتطلب إجراء</div>
                        </div>
                        <i class="fas fa-exclamation-triangle stat-icon"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Processing Statistics -->
        <div class="row mb-4">
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-day text-primary mb-2" style="font-size: 2rem;"></i>
                        <h4 class="text-primary">@Model.Statistics.ProcessedOrdersToday</h4>
                        <p class="text-muted mb-0">معالجة اليوم</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-week text-success mb-2" style="font-size: 2rem;"></i>
                        <h4 class="text-success">@Model.Statistics.ProcessedOrdersThisWeek</h4>
                        <p class="text-muted mb-0">معالجة هذا الأسبوع</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-alt text-info mb-2" style="font-size: 2rem;"></i>
                        <h4 class="text-info">@Model.Statistics.ProcessedOrdersThisMonth</h4>
                        <p class="text-muted mb-0">معالجة هذا الشهر</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Routing Statistics -->
        <div class="row mb-4">
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-route text-success mb-2" style="font-size: 2rem;"></i>
                        <h4 class="text-success">@Model.Statistics.OrdersAutoRouted</h4>
                        <p class="text-muted mb-0">التوجيه التلقائي</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-hand-paper text-warning mb-2" style="font-size: 2rem;"></i>
                        <h4 class="text-warning">@Model.Statistics.OrdersManuallyRouted</h4>
                        <p class="text-muted mb-0">التوجيه اليدوي</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card dashboard-card">
                    <div class="card-body text-center">
                        <i class="fas fa-undo text-info mb-2" style="font-size: 2rem;"></i>
                        <h4 class="text-info">@Model.Statistics.OrdersRestored</h4>
                        <p class="text-muted mb-0">الطلبات المستعادة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Departments and Supervisor Performance -->
        <div class="row mb-4">
            <div class="col-12 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0"><i class="fas fa-building me-2"></i>الأقسام الأكثر طلباً</h5>
                    </div>
                    <div class="card-body">
                        @foreach (var dept in Model.Statistics.TopDepartments.Take(5))
                        {
                            <div class="workflow-stage fade-in mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span class="fw-bold">@dept.DepartmentName</span>
                                    <span class="text-muted">@dept.Count طلب</span>
                                </div>
                                <div class="progress-modern">
                                    <div class="progress-bar bg-info" style="width: @dept.Percentage.ToString("F1")%; --progress-color: #17a2b8; --progress-color-light: #5bc0de;"></div>
                                </div>
                                <small class="text-muted">
                                    معلق: @dept.PendingCount | مكتمل: @dept.CompletedCount | @dept.Percentage.ToString("F1")%
                                </small>
                            </div>
                        }
                    </div>
                </div>
            </div>
            
        </div>

        <!-- Recent Activities -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header bg-transparent">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>النشاطات الأخيرة</h5>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            @foreach (var activity in Model.Statistics.RecentActivities)
                            {
                                <div class="timeline-item">
                                    <div class="timeline-marker" style="background-color: @activity.Color;">
                                        <i class="@activity.Icon"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">@activity.Action</h6>
                                                <p class="mb-1">@activity.ActionDescription</p>
                                                <small class="text-muted">
                                                    طلب #@activity.OrderId | @activity.EmployeeName | @activity.Department
                                                </small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge" style="background-color: @activity.Color;">@activity.Status</span>
                                                <small class="text-muted d-block">@activity.Timestamp.ToString("dd/MM/yyyy HH:mm")</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@section Styles {
    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -35px;
            top: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .timeline-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

    </style>
}

