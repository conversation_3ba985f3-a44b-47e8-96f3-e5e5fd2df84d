﻿
@{
    var currentController = ViewContext.RouteData.Values["Controller"]?.ToString() ?? "";
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewData["Title"] - Dashboard</title>

    @* Core Styles *@
    <partial name="_StylesPartial" />

    @* Page Specific Styles *@
    @await RenderSectionAsync("Styles", required: false)
</head>
<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">
        @* Top Navigation Bar *@
        <partial name="_NavbarPartial" />

        @* Sidebar Navigation *@
        <partial name="_SidebarPartial" model="currentController" />

        @* Main Content Area *@
        <main class="content-wrapper">
            @RenderBody()
        </main>
    </div>

    <!-- Toast Notification Container (bottom left) -->
    <div aria-live="polite" aria-atomic="true" style="position: fixed; bottom: 1rem; left: 1rem; z-index: 1080; min-width: 300px;">
        <div id="toastContainer" class="toast-container"></div>
    </div>

    @* Core Scripts *@
    <partial name="_ScriptsPartial" />
   
    @* Page Specific Scripts *@
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
