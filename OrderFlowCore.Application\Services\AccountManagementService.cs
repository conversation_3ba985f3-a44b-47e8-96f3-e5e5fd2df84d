using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using System.IO;
using OfficeOpenXml;

namespace OrderFlowCore.Application.Services;

public class AccountManagementService : IAccountManagementService
{
    private readonly IUserService _userService;
    private readonly IRoleService _roleService;
    private readonly IValidationService _validationService;
    private readonly ILogger<AccountManagementService> _logger;

    public AccountManagementService(
        IUserService userService,
        IRoleService roleService,
        IValidationService validationService,
        ILogger<AccountManagementService> logger)
    {
        _userService = userService;
        _roleService = roleService;
        _validationService = validationService;
        _logger = logger;
    }

    public async Task<ServiceResult<AccountsDto>> GetAccountsDataAsync()
    {
        try
        {
            var dto = new AccountsDto();
            await LoadAccountsData(dto);
            // Populate RoleTypes for the view
            dto.RoleTypes[1] = _roleService.GetAvailableRoleTypes(UserRole.DirectManager);
            dto.RoleTypes[2] = _roleService.GetAvailableRoleTypes(UserRole.AssistantManager);
            dto.RoleTypes[3] = _roleService.GetAvailableRoleTypes(UserRole.Coordinator);
            dto.RoleTypes[4] = _roleService.GetAvailableRoleTypes(UserRole.Supervisor);
            dto.RoleTypes[5] = _roleService.GetAvailableRoleTypes(UserRole.Manager);
            return ServiceResult<AccountsDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading accounts data");
            return ServiceResult<AccountsDto>.Failure("حدث خطأ أثناء تحميل بيانات الحسابات");
        }
    }

    public async Task<ServiceResult> CreateUserAsync(UserCreateDto dto)
    {
        try
        {
            
            var userDto = new UserDto
            {
                Username = dto.Username,
                Email = dto.Email,
                Phone = dto.Phone,
                Password = dto.Password, 
                UserRole = dto.UserRole,
                RoleType = dto.RoleType
            };

            var result = await _userService.CreateUserAsync(userDto);
            if (result.IsSuccess)
            {
                return ServiceResult.Success("تم إضافة المستخدم بنجاح");
            }

            return ServiceResult.Failure(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating user {Username}", dto.Username);
            return ServiceResult.Failure("حدث خطأ أثناء إضافة المستخدم");
        }
    }

    public async Task<ServiceResult> UpdateUserAsync(UserEditDto dto)
    {
        try
        {
            // Get existing user
            var existingUserResult = await _userService.GetUserByUsernameAsync(dto.Username);
            if (!existingUserResult.IsSuccess || existingUserResult.Data == null)
            {
                return ServiceResult.Failure("المستخدم غير موجود");
            }

            var userDto = existingUserResult.Data;
            userDto.Email = dto.Email ?? string.Empty;
            userDto.Phone = dto.Phone;

            // Let UserService handle all validation
            var result = await _userService.UpdateUserAsync(userDto);
            if (result.IsSuccess)
            {
                return ServiceResult.Success("تم تحديث المستخدم بنجاح");
            }

            return ServiceResult.Failure(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user {Username}", dto.Username);
            return ServiceResult.Failure("حدث خطأ أثناء تحديث المستخدم");
        }
    }

    public async Task<ServiceResult> DeleteUserAsync(string username)
    {
        try
        {
            // Let UserService handle protected user validation
            var result = await _userService.DeleteUserAsync(username);
            if (result.IsSuccess)
            {
                return ServiceResult.Success("تم حذف المستخدم بنجاح");
            }

            return ServiceResult.Failure(result.Message);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user {Username}", username);
            return ServiceResult.Failure("حدث خطأ أثناء حذف المستخدم");
        }
    }

    public async Task<ServiceResult<AccountsDto>> SearchUsersAsync(string searchTerm)
    {
        try
        {
            var dto = new AccountsDto
            {
                UserSearchTerm = searchTerm
            };

            if (!string.IsNullOrEmpty(searchTerm))
            {
                var usersResult = await _userService.GetAllUsersAsync();
                if (usersResult.IsSuccess)
                {
                    dto.Users = usersResult.Data?
                        .Where(u => u.Username != "Super Admin" &&
                                  u.Username.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                        .ToList() ?? [];
                }
            }
            else
            {
                await LoadAccountsData(dto);
            }
            // Populate RoleTypes for the view
            dto.RoleTypes[1] = _roleService.GetAvailableRoleTypes(UserRole.DirectManager);
            dto.RoleTypes[2] = _roleService.GetAvailableRoleTypes(UserRole.AssistantManager);
            dto.RoleTypes[3] = _roleService.GetAvailableRoleTypes(UserRole.Coordinator);
            dto.RoleTypes[4] = _roleService.GetAvailableRoleTypes(UserRole.Supervisor);
            dto.RoleTypes[5] = _roleService.GetAvailableRoleTypes(UserRole.Manager);
            return ServiceResult<AccountsDto>.Success(dto);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching users with term {SearchTerm}", searchTerm);
            return ServiceResult<AccountsDto>.Failure("حدث خطأ أثناء البحث");
        }
    }

    public async Task<ServiceResult> ChangeUserPasswordAsync(AdminChangePasswordDto dto)
    {
        // Use validation service for password confirmation
        var confirmationValidation = _validationService.ValidatePasswordConfirmation(dto.NewPassword, dto.ConfirmPassword);
        if (!confirmationValidation.IsSuccess)
            return confirmationValidation;

        // Let UserService handle password validation and setting
        var result = await _userService.SetUserPasswordAsync(dto.Username, dto.NewPassword);
        if (result.IsSuccess)
            return ServiceResult.Success("تم تغيير كلمة المرور بنجاح");
        return ServiceResult.Failure(result.Message);
    }

    public bool IsValidEmail(string email)
    {
        // Delegate to validation service
        var result = _validationService.ValidateEmail(email);
        return result.IsSuccess;
    }


    private async Task LoadAccountsData(AccountsDto dto)
    {
        var usersResult = await _userService.GetAllUsersAsync();
        if (usersResult.IsSuccess)
        {
            dto.Users = usersResult.Data?.Where(u => u.Username != "Super Admin").ToList() ?? [];
        }
    }

    public async Task<ServiceResult<byte[]>> ExportUsersToExcelAsync()
    {
        try
        {
            var usersResult = await _userService.GetAllUsersAsync();
            if (!usersResult.IsSuccess)
            {
                return ServiceResult<byte[]>.Failure("فشل في استرجاع بيانات المستخدمين");
            }

            var users = usersResult.Data?.Where(u => u.Username != "Super Admin").ToList() ?? [];

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("المستخدمين");

            // Headers in Arabic
            worksheet.Cells[1, 1].Value = "اسم المستخدم";
            worksheet.Cells[1, 2].Value = "البريد الإلكتروني";
            worksheet.Cells[1, 3].Value = "رقم الهاتف";
            worksheet.Cells[1, 4].Value = "الدور الرئيسي";
            worksheet.Cells[1, 5].Value = "تخصص الدور";

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, 5])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
                range.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
            }

            // Data rows
            int row = 2;
            foreach (var user in users)
            {
                worksheet.Cells[row, 1].Value = user.Username;
                worksheet.Cells[row, 2].Value = user.Email;
                worksheet.Cells[row, 3].Value = user.Phone;
                worksheet.Cells[row, 4].Value = GetUserRoleDisplayName(user.UserRole);
                worksheet.Cells[row, 5].Value = user.RoleType ?? "";
                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            var excelData = package.GetAsByteArray();
            return ServiceResult<byte[]>.Success(excelData, "تم تصدير المستخدمين بنجاح");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting users to Excel");
            return ServiceResult<byte[]>.Failure($"خطأ في تصدير المستخدمين: {ex.Message}");
        }
    }

    public async Task<ServiceResult<int>> ImportUsersFromExcelAsync(Stream excelStream)
    {
        try
        {
            int importedCount = 0;
            var errors = new List<string>();

            using var package = new ExcelPackage(excelStream);
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();

            if (worksheet == null)
            {
                return ServiceResult<int>.Failure("لم يتم العثور على ورقة عمل في الملف");
            }

            // Check if we have data
            if (worksheet.Dimension == null || worksheet.Dimension.Rows < 2)
            {
                return ServiceResult<int>.Failure("الملف فارغ أو لا يحتوي على بيانات");
            }

            // Process each row (skip header row)
            for (int row = 2; row <= worksheet.Dimension.Rows; row++)
            {
                try
                {
                    var username = worksheet.Cells[row, 1].Value?.ToString()?.Trim();
                    var email = worksheet.Cells[row, 2].Value?.ToString()?.Trim();
                    var phone = worksheet.Cells[row, 3].Value?.ToString()?.Trim();
                    var roleText = worksheet.Cells[row, 4].Value?.ToString()?.Trim();
                    var roleType = worksheet.Cells[row, 5].Value?.ToString()?.Trim();

                    // Validate required fields
                    if (string.IsNullOrEmpty(username))
                    {
                        errors.Add($"الصف {row}: اسم المستخدم مطلوب");
                        continue;
                    }

                    // Check if user already exists
                    var existingUserResult = await _userService.GetUserByUsernameAsync(username);
                    if (existingUserResult.IsSuccess)
                    {
                        errors.Add($"الصف {row}: المستخدم {username} موجود بالفعل");
                        continue;
                    }

                    // Parse user role
                    var userRole = ParseUserRole(roleText);
                    if (userRole == null)
                    {
                        errors.Add($"الصف {row}: دور المستخدم غير صحيح: {roleText}");
                        continue;
                    }

                    // Create user with default password
                    var userCreateDto = new UserCreateDto
                    {
                        Username = username,
                        Email = email,
                        Phone = phone,
                        Password = "123456", // Default password - should be changed on first login
                        UserRole = userRole.Value,
                        RoleType = roleType
                    };

                    var createResult = await CreateUserAsync(userCreateDto);
                    if (createResult.IsSuccess)
                    {
                        importedCount++;
                    }
                    else
                    {
                        errors.Add($"الصف {row}: فشل في إضافة المستخدم {username} - {createResult.Message}");
                    }
                }
                catch (Exception ex)
                {
                    errors.Add($"الصف {row}: خطأ في معالجة البيانات - {ex.Message}");
                }
            }

            var message = $"تم استيراد {importedCount} مستخدم بنجاح";
            if (errors.Any())
            {
                message += $". عدد الأخطاء: {errors.Count}";
            }

            var finalResult = ServiceResult<int>.Success(importedCount, message);
            finalResult.Errors = errors;
            return finalResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing users from Excel");
            return ServiceResult<int>.Failure($"خطأ في استيراد المستخدمين: {ex.Message}");
        }
    }

    private string GetUserRoleDisplayName(UserRole role)
    {
        return role switch
        {
            UserRole.DirectManager => "مدير مباشر",
            UserRole.AssistantManager => "مساعد مدير",
            UserRole.Coordinator => "منسق",
            UserRole.Supervisor => "مشرف",
            UserRole.Manager => "مدير",
            _ => "غير محدد"
        };
    }

    private UserRole? ParseUserRole(string roleText)
    {
        if (string.IsNullOrEmpty(roleText))
            return UserRole.DirectManager; // Default role

        return roleText.ToLower() switch
        {
            "مدير مباشر" or "direct manager" => UserRole.DirectManager,
            "مساعد مدير" or "assistant manager" => UserRole.AssistantManager,
            "منسق" or "coordinator" => UserRole.Coordinator,
            "مشرف" or "supervisor" => UserRole.Supervisor,
            "مدير" or "manager" => UserRole.Manager,
            _ => null
        };
    }
}
