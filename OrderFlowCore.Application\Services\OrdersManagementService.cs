using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OfficeOpenXml;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Application.Services
{
    public class OrdersManagementService : IOrdersManagementService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<OrdersManagementService> _logger;
        private readonly IFileService _fileService;

        public OrdersManagementService(IUnitOfWork unitOfWork, ILogger<OrdersManagementService> logger, IFileService fileService)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _fileService = fileService;
        }

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetFilteredOrdersAsync(OrderFilterDto filter)
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetAllAsync();
                
                // Apply filters
                var filteredOrders = orders.AsQueryable();

                if (filter.Status != "All" && !string.IsNullOrEmpty(filter.Status))
                {
                    if (Enum.TryParse<OrderStatus>(filter.Status, out var parsedStatus))
                    {
                        filteredOrders = filteredOrders.Where(o => o.OrderStatus == parsedStatus);
                    }
                }

                if (filter.Department != "All" && !string.IsNullOrEmpty(filter.Department))
                {
                    filteredOrders = filteredOrders.Where(o => o.Department == filter.Department);
                }

                if (filter.FromDate.HasValue)
                {
                    filteredOrders = filteredOrders.Where(o => o.CreatedAt >= filter.FromDate.Value);
                }

                if (filter.ToDate.HasValue)
                {
                    filteredOrders = filteredOrders.Where(o => o.CreatedAt <= filter.ToDate.Value);
                }

                if (!string.IsNullOrEmpty(filter.CivilRecord))
                {
                    filteredOrders = filteredOrders.Where(o => o.CivilRecord.Contains(filter.CivilRecord));
                }

                var result = filteredOrders.Select(o => new OrderSummaryDto
                {
                    Id = o.Id,
                    EmployeeName = o.EmployeeName ?? string.Empty,
                    Department = o.Department ?? string.Empty,
                    OrderStatus = o.OrderStatus,
                    CreatedAt = o.CreatedAt,
                    OrderType = o.OrderType ?? string.Empty,
                    CivilRecord = o.CivilRecord ?? string.Empty
                }).OrderByDescending(o => o.CreatedAt).ToList();

                return ServiceResult<List<OrderSummaryDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الطلبات المفلترة");
                return ServiceResult<List<OrderSummaryDto>>.Failure("حدث خطأ أثناء جلب الطلبات");
            }
        }

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetAllOrdersAsync()
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetAllAsync();
                var result = orders.Select(o => new OrderSummaryDto
                {
                    Id = o.Id,
                    EmployeeName = o.EmployeeName ?? string.Empty,
                    Department = o.Department ?? string.Empty,
                    OrderStatus = o.OrderStatus,
                    CreatedAt = o.CreatedAt,
                    OrderType = o.OrderType ?? string.Empty,
                    CivilRecord = o.CivilRecord ?? string.Empty
                }).OrderByDescending(o => o.CreatedAt).ToList();

                return ServiceResult<List<OrderSummaryDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب جميع الطلبات");
                return ServiceResult<List<OrderSummaryDto>>.Failure("حدث خطأ أثناء جلب الطلبات");
            }
        }

        public async Task<ServiceResult<List<DropdownItemDto>>> GetOrderStatusesAsync()
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetAllAsync();
                var statuses = orders.Select(o => o.OrderStatus)
                    .Distinct()
                    .Select(s => new DropdownItemDto { Value = s.ToString(), Text = s.ToDisplayString() })
                    .OrderBy(s => s.Text)
                    .ToList();

                statuses.Insert(0, new DropdownItemDto { Value = "All", Text = "كل الحالات" });
                return ServiceResult<List<DropdownItemDto>>.Success(statuses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب حالات الطلبات");
                return ServiceResult<List<DropdownItemDto>>.Failure("حدث خطأ أثناء جلب حالات الطلبات");
            }
        }

        public async Task<ServiceResult<List<DropdownItemDto>>> GetDepartmentsAsync()
        {
            try
            {
                var departments = await _unitOfWork.Departments.GetAllAsync();
                var result = departments.Select(d => new DropdownItemDto
                {
                    Value = d.Name,
                    Text = d.Name
                }).OrderBy(d => d.Text).ToList();

                result.Insert(0, new DropdownItemDto { Value = "All", Text = "كل الأقسام" });
                return ServiceResult<List<DropdownItemDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الأقسام");
                return ServiceResult<List<DropdownItemDto>>.Failure("حدث خطأ أثناء جلب الأقسام");
            }
        }

        public async Task<ServiceResult<List<DropdownItemDto>>> GetOrderNumbersAsync()
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetAllAsync();
                var result = orders.Select(o => new DropdownItemDto
                {
                    Value = o.Id.ToString(),
                    Text = $"{o.Id} - {o.EmployeeName}"
                }).OrderByDescending(o => int.Parse(o.Value)).ToList();

                return ServiceResult<List<DropdownItemDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب أرقام الطلبات");
                return ServiceResult<List<DropdownItemDto>>.Failure("حدث خطأ أثناء جلب أرقام الطلبات");
            }
        }

        public async Task<ServiceResult<byte[]>> ExportOrdersToExcelAsync(OrderFilterDto filter)
        {
            try
            {
                var ordersResult = await GetFilteredOrdersAsync(filter);
                if (!ordersResult.IsSuccess)
                {
                    return ServiceResult<byte[]>.Failure(ordersResult.Message);
                }

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("الطلبات");

                // Headers
                worksheet.Cells[1, 1].Value = "رقم الطلب";
                worksheet.Cells[1, 2].Value = "اسم الموظف";
                worksheet.Cells[1, 3].Value = "القسم";
                worksheet.Cells[1, 4].Value = "حالة الطلب";
                worksheet.Cells[1, 5].Value = "تاريخ الطلب";
                worksheet.Cells[1, 6].Value = "نوع الطلب";
                worksheet.Cells[1, 7].Value = "السجل المدني";

                // Data
                for (int i = 0; i < ordersResult.Data.Count; i++)
                {
                    var order = ordersResult.Data[i];
                    worksheet.Cells[i + 2, 1].Value = order.Id;
                    worksheet.Cells[i + 2, 2].Value = order.EmployeeName;
                    worksheet.Cells[i + 2, 3].Value = order.Department;
                    worksheet.Cells[i + 2, 4].Value = order.OrderStatus.ToDisplayString();
                    worksheet.Cells[i + 2, 5].Value = order.CreatedAt.ToString("yyyy-MM-dd");
                    worksheet.Cells[i + 2, 6].Value = order.OrderType;
                    worksheet.Cells[i + 2, 7].Value = order.CivilRecord;
                }

                worksheet.Cells.AutoFitColumns();
                return ServiceResult<byte[]>.Success(package.GetAsByteArray());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير الطلبات إلى Excel");
                return ServiceResult<byte[]>.Failure("حدث خطأ أثناء تصدير الطلبات");
            }
        }

        public async Task<ServiceResult<byte[]>> ExportFilteredResultsToExcelAsync(OrderFilterDto filter)
        {
            return await ExportOrdersToExcelAsync(filter);
        }

        public async Task<ServiceResult<byte[]>> ExportDetailedStatisticsToExcelAsync()
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetAllAsync();
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("الإحصائيات المفصلة");

                int row = 1;

                StyleHeader(worksheet, row);
                worksheet.Cells[row++, 1].Value = "تقرير الإحصائيات المفصلة";
                worksheet.Cells[row++, 1].Value = $"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm}";
                row++;

                // Total Orders
                worksheet.Cells[row, 1].Value = "إجمالي الطلبات";
                worksheet.Cells[row++, 2].Value = orders.Count;
                row++;

                // Orders by Status
                StyleHeader(worksheet, row);
                worksheet.Cells[row++, 1].Value = "عدد الطلبات حسب الحالة";
                worksheet.Cells[row, 1].Value = "الحالة";
                worksheet.Cells[row, 2].Value = "العدد";
                worksheet.Cells[row, 3].Value = "%";
                var totalOrders = orders.Count;
                var statusGroups = orders.GroupBy(o => o.OrderStatus)
                    .Select(g => new { Status = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .ToList();
                int statusStartRow = ++row;
                foreach (var group in statusGroups)
                {
                    worksheet.Cells[row, 1].Value = group.Status.ToDisplayString();
                    worksheet.Cells[row, 2].Value = group.Count;
                    worksheet.Cells[row, 3].Value = totalOrders > 0 ? (double)group.Count / totalOrders : 0;
                    row++;
                }
                row++;

                // Orders by Department
                StyleHeader(worksheet, row);
                worksheet.Cells[row++, 1].Value = "عدد الطلبات حسب القسم";
                worksheet.Cells[row, 1].Value = "القسم";
                worksheet.Cells[row, 2].Value = "العدد";
                worksheet.Cells[row, 3].Value = "%";
                var deptGroups = orders.GroupBy(o => o.Department ?? "غير محدد")
                    .Select(g => new { Department = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .ToList();
                int deptStartRow = ++row;
                foreach (var group in deptGroups)
                {
                    worksheet.Cells[row, 1].Value = group.Department;
                    worksheet.Cells[row, 2].Value = group.Count;
                    worksheet.Cells[row, 3].Value = totalOrders > 0 ? (double)group.Count / totalOrders : 0;
                    row++;
                }
                row++;

                // Orders by Type
                StyleHeader(worksheet, row);
                worksheet.Cells[row++, 1].Value = "عدد الطلبات حسب نوع الطلب";
                worksheet.Cells[row, 1].Value = "نوع الطلب";
                worksheet.Cells[row, 2].Value = "العدد";
                worksheet.Cells[row, 3].Value = "%";
                var typeGroups = orders.GroupBy(o => o.OrderType ?? "غير محدد")
                    .Select(g => new { OrderType = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .ToList();
                int typeStartRow = ++row;
                foreach (var group in typeGroups)
                {
                    worksheet.Cells[row, 1].Value = group.OrderType;
                    worksheet.Cells[row, 2].Value = group.Count;
                    worksheet.Cells[row, 3].Value = totalOrders > 0 ? (double)group.Count / totalOrders : 0;
                    row++;
                }
                row++;

                // Orders per Month (last 12 months)
                StyleHeader(worksheet, row);
                worksheet.Cells[row++, 1].Value = "عدد الطلبات لكل شهر (آخر 12 شهر)";
                worksheet.Cells[row, 1].Value = "الشهر";
                worksheet.Cells[row, 2].Value = "العدد";
                var now = DateTime.Now;
                var months = Enumerable.Range(0, 12)
                    .Select(i => now.AddMonths(-i))
                    .Select(d => new { Year = d.Year, Month = d.Month })
                    .OrderBy(x => x.Year).ThenBy(x => x.Month)
                    .ToList();
                int monthStartRow = ++row;
                foreach (var m in months)
                {
                    var count = orders.Count(o => o.CreatedAt.Year == m.Year && o.CreatedAt.Month == m.Month);
                    worksheet.Cells[row, 1].Value = $"{m.Year}-{m.Month:D2}";
                    worksheet.Cells[row, 2].Value = count;
                    row++;
                }

                worksheet.Cells[1, 1, row, 3].AutoFitColumns();
                return ServiceResult<byte[]>.Success(package.GetAsByteArray());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير الإحصائيات المفصلة");
                return ServiceResult<byte[]>.Failure("حدث خطأ أثناء تصدير الإحصائيات");
            }
        }

        private void StyleHeader(ExcelWorksheet worksheet, int row)
        {
            worksheet.Cells[row, 1, row, 3].Merge = true;
            worksheet.Cells[row, 1].Style.Font.Bold = true;
            worksheet.Cells[row, 1].Style.Font.Size = 12;
            worksheet.Cells[row, 1].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[row, 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
            worksheet.Cells[row, 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
        }

        public async Task<ServiceResult> DeleteOldOrdersAsync(DeleteOldOrdersDto deleteRequest, string userName)
        {
            try
            {
                DateTime cutoffDate;
                List<OrdersTable> ordersToDelete;
                
                if (deleteRequest.UseCustomDateRange)
                {
                    if (!deleteRequest.StartDate.HasValue || !deleteRequest.EndDate.HasValue)
                    {
                        return ServiceResult.Failure("يجب تحديد تاريخ البداية والنهاية");
                    }
                    
                    ordersToDelete = await _unitOfWork.Orders.GetOrdersByDateRangeAsync(
                        deleteRequest.StartDate.Value, deleteRequest.EndDate.Value);
                    
                }
                else
                {
                    cutoffDate = DateTime.Now.AddMonths(-deleteRequest.PeriodInMonths);
                    ordersToDelete = await _unitOfWork.Orders.GetOrdersOlderThanAsync(cutoffDate);
                }

                foreach (var order in ordersToDelete)
                {
                    var fileUrls = new List<string>();
                    if (!string.IsNullOrEmpty(order.File1Url)) fileUrls.Add(order.File1Url);
                    if (!string.IsNullOrEmpty(order.File2Url)) fileUrls.Add(order.File2Url);
                    if (!string.IsNullOrEmpty(order.File3Url)) fileUrls.Add(order.File3Url);
                    if (!string.IsNullOrEmpty(order.File4Url)) fileUrls.Add(order.File4Url);
                    if (fileUrls.Count > 0)
                    {
                        var fileDeleteResult = await _fileService.DeleteFilesAsync(fileUrls);
                        if (!fileDeleteResult.IsSuccess)
                        {
                            _logger.LogWarning("فشل حذف بعض الملفات للطلب {OrderId}: {Message}", order.Id, fileDeleteResult.Message);
                        }
                    }
                    await _unitOfWork.Orders.DeleteAsync(order.Id);
                }

                await _unitOfWork.SaveChangesAsync();
                
                _logger.LogInformation("تم حذف الطلبات القديمة بواسطة {UserName}", userName);
                return ServiceResult.Success("تم حذف الطلبات القديمة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الطلبات القديمة بواسطة {UserName}", userName);
                return ServiceResult.Failure("حدث خطأ أثناء حذف الطلبات القديمة");
            }
        }

        public async Task<ServiceResult> DeleteSpecificOrderAsync(int orderId, string userName)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }
                var fileUrls = new List<string>();
                if (!string.IsNullOrEmpty(order.File1Url)) fileUrls.Add(order.File1Url);
                if (!string.IsNullOrEmpty(order.File2Url)) fileUrls.Add(order.File2Url);
                if (!string.IsNullOrEmpty(order.File3Url)) fileUrls.Add(order.File3Url);
                if (!string.IsNullOrEmpty(order.File4Url)) fileUrls.Add(order.File4Url);
                if (fileUrls.Count > 0)
                {
                    var fileDeleteResult = await _fileService.DeleteFilesAsync(fileUrls);
                    if (!fileDeleteResult.IsSuccess)
                    {
                        _logger.LogWarning("فشل حذف بعض الملفات للطلب {OrderId}: {Message}", orderId, fileDeleteResult.Message);
                    }
                }
                await _unitOfWork.Orders.DeleteAsync(orderId);
                await _unitOfWork.SaveChangesAsync();
                
                _logger.LogInformation("تم حذف الطلب {OrderId} بواسطة {UserName}", orderId, userName);
                return ServiceResult.Success("تم حذف الطلب بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الطلب {OrderId} بواسطة {UserName}", orderId, userName);
                return ServiceResult.Failure("حدث خطأ أثناء حذف الطلب");
            }
        }
    }
}
