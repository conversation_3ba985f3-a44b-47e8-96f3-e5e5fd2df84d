using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using System.IO;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IAccountManagementService
{
    /// <summary>
    /// Loads all accounts data including users and departments
    /// </summary>
    Task<ServiceResult<AccountsDto>> GetAccountsDataAsync();

    /// <summary>
    /// Creates a new user account
    /// </summary>
    Task<ServiceResult> CreateUserAsync(UserCreateDto dto);

    /// <summary>
    /// Updates an existing user account
    /// </summary>
    Task<ServiceResult> UpdateUserAsync(UserEditDto dto);

    /// <summary>
    /// Deletes a user account
    /// </summary>
    Task<ServiceResult> DeleteUserAsync(string username);
    /// <summary>
    /// Searches for users by search term
    /// </summary>
    Task<ServiceResult<AccountsDto>> SearchUsersAsync(string searchTerm);

    /// <summary>
    /// Allows admin to change a user's password
    /// </summary>
    Task<ServiceResult> ChangeUserPasswordAsync(AdminChangePasswordDto dto);

    /// <summary>
    /// Validates email format and domain
    /// </summary>
    bool IsValidEmail(string email);

    /// <summary>
    /// Exports all users to Excel format
    /// </summary>
    Task<ServiceResult<byte[]>> ExportUsersToExcelAsync();

    /// <summary>
    /// Imports users from Excel file
    /// </summary>
    Task<ServiceResult<int>> ImportUsersFromExcelAsync(Stream excelStream);

}
