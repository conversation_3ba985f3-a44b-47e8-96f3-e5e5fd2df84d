using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    public class SupervisorOrderService : ISupervisorOrderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ISupervisorService _supervisorService;
        private readonly ILogger<SupervisorOrderService> _logger;

        public SupervisorOrderService(
            IUnitOfWork unitOfWork,
            ISupervisorService supervisorService,
            ILogger<SupervisorOrderService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _supervisorService = supervisorService ?? throw new ArgumentNullException(nameof(supervisorService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetSupervisorOrdersAsync(string supervisorRole)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(supervisorRole))
                {
                    return ServiceResult<List<OrderSummaryDto>>.Success(new List<OrderSummaryDto>());
                }

                // Get orders for supervisors
                var allOrders = await _unitOfWork.Orders.GetSupervisorsPendingOrdersAsync();

                var filtered = new List<OrdersTable>();

                foreach (var order in allOrders)
                {
                    // Get the value of the supervisor column for this role
                    var supervisorStatuses = _supervisorService.GetAssignedSupervisors(order);
                    // If this order is assigned to this supervisor
                    if (supervisorStatuses.Contains(supervisorRole))
                    {
                        // Get the actual value in the property
                        var status = _supervisorService.GetSupervisorStatus(order, supervisorRole);
                        if (!string.IsNullOrEmpty(status) &&
                            (status.Contains("الطلب قيد التنفيذ") || status.Contains("يتطلب إجراء")))
                        {
                            filtered.Add(order);
                        }
                    }
                }

                var orderSummaries = filtered
                    .OrderByDescending(o => o.CreatedAt)
                    .Select(order => new OrderSummaryDto
                    {
                        Id = order.Id,
                        EmployeeName = order.EmployeeName,
                        OrderType = order.OrderType,
                        Department = order.Department,
                        CreatedAt = order.CreatedAt,
                        OrderStatus = order.OrderStatus
                    }).ToList();

                return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting supervisor orders for role {SupervisorRole}", supervisorRole);
                return ServiceResult<List<OrderSummaryDto>>.Failure($"حدث خطأ أثناء جلب الطلبات: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ConfirmOrderBySupervisorAsync(int orderId, string supervisorRole, string userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(supervisorRole))
                {
                    return ServiceResult.Failure("دور المشرف مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Update supervisor status to confirmed
                var confirmedStatus = OrderHelper.ConfirmedBy(userName);
                _supervisorService.UpdateSupervisorStatuses(order, new List<string> { supervisorRole }, confirmedStatus);

                // Check if all required supervisors have confirmed
                var allConfirmed = CheckAllSupervisorsConfirmed(order);
                if (allConfirmed)
                {
                    order.OrderStatus = OrderStatus.D;
                }

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                var message = allConfirmed ? "تم اعتماد الطلب بنجاح من جميع المشرفين" : "تم اعتماد الطلب من المشرف بنجاح";
                return ServiceResult.Success(message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming order {OrderId} by supervisor {SupervisorRole}", orderId, supervisorRole);
                return ServiceResult.Failure($"حدث خطأ أثناء اعتماد الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> NeedsActionBySupervisorAsync(int orderId, string actionRequired, string supervisorRole, string userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(actionRequired))
                {
                    return ServiceResult.Failure("تفاصيل الإجراء المطلوب مطلوبة");
                }

                if (string.IsNullOrWhiteSpace(supervisorRole))
                {
                    return ServiceResult.Failure("دور المشرف مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Update supervisor status to needs action
                var actionStatus = OrderHelper.OrderNeedActionBySupervisor(userName);
                _supervisorService.UpdateSupervisorStatuses(order, new List<string> { supervisorRole }, actionStatus);

                // Update order status to action required by supervisor
                order.OrderStatus = OrderStatus.ActionRequiredBySupervisor;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم تسجيل طلب الإجراء بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting action required for order {OrderId} by supervisor {SupervisorRole}", orderId, supervisorRole);
                return ServiceResult.Failure($"حدث خطأ أثناء تسجيل طلب الإجراء: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ReturnOrderBySupervisorAsync(int orderId, string returnReason, string supervisorRole, string userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(returnReason))
                {
                    return ServiceResult.Failure("سبب الرفض مطلوب");
                }

                if (string.IsNullOrWhiteSpace(supervisorRole))
                {
                    return ServiceResult.Failure("دور المشرف مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Clear all status
                _supervisorService.ClearUnderImplementationStatuses(order);

                // Update supervisor status to Returned
                var returnedStatus = OrderHelper.ReturnedBy(userName);
                _supervisorService.UpdateSupervisorStatuses(order, new List<string> { supervisorRole }, returnedStatus);

                // Update order status and reason
                order.OrderStatus = OrderStatus.ReturnedBySupervisor;
                order.ReasonForCancellation = returnReason;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم رفض الطلب بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting order {OrderId} by supervisor {SupervisorRole}", orderId, supervisorRole);
                return ServiceResult.Failure($"حدث خطأ أثناء رفض الطلب: {ex.Message}");
            }
        }

        #region Private Helper Methods

        private bool CheckAllSupervisorsConfirmed(OrdersTable order)
        {
            // Get all assigned supervisors
            var assignedSupervisors = _supervisorService.GetAssignedSupervisors(order);

            // Check if all assigned supervisors have confirmed (status contains "اعتماد")
            var confirmedSupervisors = assignedSupervisors.Count(supervisorRole =>
            {
                var status = _supervisorService.GetSupervisorStatus(order, supervisorRole);
                return !string.IsNullOrEmpty(status) && status.Contains("اعتماد");
            });

            return confirmedSupervisors >= assignedSupervisors.Count;
        }

        #endregion
    }
}
