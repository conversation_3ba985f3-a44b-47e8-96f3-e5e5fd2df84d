using Microsoft.AspNetCore.Mvc.Rendering;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Web.ViewModels
{
    public class OrdersManagementViewModel
    {
        public OrdersManagementViewModel()
        {
            StatusOptions = new List<SelectListItem>();
            DepartmentOptions = new List<SelectListItem>();
            OrderNumbers = new List<SelectListItem>();
            Orders = new List<OrderSummaryDto>();
            DeletePeriodOptions = new List<SelectListItem>
            {
                new SelectListItem { Value = "3", Text = "3 أشهر" },
                new SelectListItem { Value = "6", Text = "6 أشهر" },
                new SelectListItem { Value = "9", Text = "9 أشهر" },
                new SelectListItem { Value = "12", Text = "سنة واحدة", Selected = true },
                new SelectListItem { Value = "24", Text = "سنتين" },
                new SelectListItem { Value = "36", Text = "3 سنوات" },
                new SelectListItem { Value = "-1", Text = "تحديد نطاق تاريخي" }
            };
        }

        // Filter Options
        public List<SelectListItem> StatusOptions { get; set; }
        public List<SelectListItem> DepartmentOptions { get; set; }
        public List<SelectListItem> OrderNumbers { get; set; }

        // Filter Values
        [Display(Name = "حالة الطلب")]
        public string SelectedStatus { get; set; } = "All";

        [Display(Name = "القسم")]
        public string SelectedDepartment { get; set; } = "All";

        [Display(Name = "من تاريخ")]
        [DataType(DataType.Date)]
        public DateTime? FromDate { get; set; }

        [Display(Name = "إلى تاريخ")]
        [DataType(DataType.Date)]
        public DateTime? ToDate { get; set; }

        [Display(Name = "السجل المدني")]
        public string CivilRecord { get; set; } = string.Empty;

        // Orders Data
        public List<OrderSummaryDto> Orders { get; set; }
        public int TotalOrdersCount { get; set; }

        // Admin Operations (Critical Actions)
        public bool IsAdmin { get; set; }

        // Delete Old Orders
        public List<SelectListItem> DeletePeriodOptions { get; set; }
        
        [Display(Name = "اختر الفترة")]
        public int SelectedDeletePeriod { get; set; } = 12;

        [Display(Name = "من تاريخ")]
        [DataType(DataType.Date)]
        public DateTime? DeleteStartDate { get; set; }

        [Display(Name = "إلى تاريخ")]
        [DataType(DataType.Date)]
        public DateTime? DeleteEndDate { get; set; }

        public bool ShowCustomDateRange => SelectedDeletePeriod == -1;

        // Delete Specific Order
        [Display(Name = "رقم الطلب")]
        public int? SelectedOrderToDelete { get; set; }

        // Messages
        public string SuccessMessage { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;

        // UI State
        public bool ShowFilters { get; set; } = true;
        public bool ShowAdminActions { get; set; } = true;
        public bool IsLoading { get; set; }

        // Helper Methods
        public OrderFilterDto GetFilterDto()
        {
            return new OrderFilterDto
            {
                Status = SelectedStatus,
                Department = SelectedDepartment,
                FromDate = FromDate,
                ToDate = ToDate,
                CivilRecord = CivilRecord
            };
        }

        public DeleteOldOrdersDto GetDeleteOldOrdersDto()
        {
            return new DeleteOldOrdersDto
            {
                PeriodInMonths = SelectedDeletePeriod,
                StartDate = DeleteStartDate,
                EndDate = DeleteEndDate,
                UseCustomDateRange = ShowCustomDateRange
            };
        }
    }
}
