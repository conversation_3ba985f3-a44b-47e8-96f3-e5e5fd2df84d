﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces;
using System.Security.Claims;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.Controllers;

public class AuthController : Controller
{
    private readonly IAuthService _authService;
    private readonly ILogger<AuthController> _logger;

    // Constants for claim types - better than magic strings
    private const string ROLE_TYPE_CLAIM = "RoleType";
    private const string EMAIL_CLAIM = "Email";
    private const int DEFAULT_SESSION_HOURS = 2;

    public AuthController(IAuthService authService, ILogger<AuthController> logger)
    {
        _authService = authService ?? throw new ArgumentNullException(nameof(authService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    [HttpGet]
    [AllowAnonymous]
    public IActionResult Login(string? returnUrl = null)
    {
        // Redirect authenticated users away from login page
        if (User.Identity?.IsAuthenticated == true)
        {
            _logger.LogInformation("Already authenticated user attempted to access login page");
            return RedirectToAction("Index", "Dashboard");
        }

        ViewData["ReturnUrl"] = returnUrl;
        return View();
    }

    [HttpPost]
    [AllowAnonymous]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Login(LoginDto model, string? returnUrl = null)
    {
        ViewData["ReturnUrl"] = returnUrl;

        if (!ModelState.IsValid)
        {
            _logger.LogWarning("Login attempt with invalid model state for user: {Username}",
                model?.Username);
            return View(model);
        }

        try
        {
            _logger.LogInformation("Login attempt for user: {Username}", model.Username);

            var validationResult = await _authService.ValidateUserAsync(model.Username, model.Password);

            if (!validationResult.IsSuccess)
            {
                _logger.LogWarning("Failed login attempt for user: {Username}. Reason: {Reason}",
                    model.Username, validationResult.Message);
                ModelState.AddModelError(string.Empty, "اسم المستخدم أو كلمة المرور غير صحيحة");
                return View(model);
            }

            var userResult = await _authService.GetUserByUsernameAsync(model.Username);

            if (!userResult.IsSuccess || userResult.Data == null)
            {
                _logger.LogError("User validation succeeded but user retrieval failed for: {Username}",
                    model.Username);
                ModelState.AddModelError(string.Empty, "حدث خطأ أثناء تسجيل الدخول");
                return View(model);
            }

            await SignInUserAsync(userResult.Data, model.RememberMe);

            _logger.LogInformation("Successful login for user: {Username} (ID: {UserId})",
                userResult.Data.Username, userResult.Data.Id);

            return RedirectToReturnUrlOrDefault(returnUrl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during login for user: {Username}", model.Username);
            ModelState.AddModelError(string.Empty, "حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى");
            return View(model);
        }
    }

    [HttpPost]
    [Authorize]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Logout()
    {
        var username = User.Identity?.Name;

        try
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            _logger.LogInformation("User logged out: {Username}", username);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during logout for user: {Username}", username);
        }

        return RedirectToAction("Index", "Home");
    }

    [HttpGet]
    [AllowAnonymous]
    public IActionResult AccessDenied(string? returnUrl = null)
    {
        _logger.LogWarning("Access denied for user: {Username} to URL: {ReturnUrl}",
            User.Identity?.Name, returnUrl);

        ViewData["ReturnUrl"] = returnUrl;
        return View();
    }

    #region Private Helper Methods

    private async Task SignInUserAsync(UserDto user, bool rememberMe)
    {
        var claims = CreateUserClaims(user);
        var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
        var authProperties = CreateAuthenticationProperties(rememberMe);

        await HttpContext.SignInAsync(
            CookieAuthenticationDefaults.AuthenticationScheme,
            new ClaimsPrincipal(claimsIdentity),
            authProperties);
    }

    private static List<Claim> CreateUserClaims(UserDto user)
    {
        var claims = new List<Claim>
        {
            new(ClaimTypes.Name, user.Username ?? string.Empty),
            new(ClaimTypes.NameIdentifier, user.Id.ToString() ?? string.Empty),
            new(ClaimTypes.Role, user.UserRole.ToDisplayName() ?? string.Empty),
            new(ROLE_TYPE_CLAIM, user.RoleType?.ToString() ?? string.Empty),
            new(EMAIL_CLAIM, user.Email ?? string.Empty),
            new("AuthenticationTime", DateTimeOffset.UtcNow.ToString("O")) // ISO 8601 format
        };

        // Add additional claims if needed
        if (!string.IsNullOrEmpty(user.Email))
        {
            claims.Add(new Claim(ClaimTypes.Email, user.Email));
        }

        return claims;
    }

    private static AuthenticationProperties CreateAuthenticationProperties(bool rememberMe)
    {
        var authProperties = new AuthenticationProperties
        {
            IsPersistent = rememberMe,
            ExpiresUtc = DateTimeOffset.UtcNow.AddHours(DEFAULT_SESSION_HOURS),
            AllowRefresh = true
        };

        // Set different expiration for "Remember Me"
        if (rememberMe)
        {
            authProperties.ExpiresUtc = DateTimeOffset.UtcNow.AddDays(30);
        }

        return authProperties;
    }

    private IActionResult RedirectToReturnUrlOrDefault(string? returnUrl)
    {
        if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
        {
            _logger.LogDebug("Redirecting to return URL: {ReturnUrl}", returnUrl);
            return Redirect(returnUrl);
        }

        _logger.LogDebug("Redirecting to default dashboard");
        return RedirectToAction("Index", "Dashboard");
    }

    #endregion
}


 