/**
 * AssistantManager View JavaScript
 * Handles order management functionality for the AssistantManager view
 */

document.addEventListener("DOMContentLoaded", function () {
    // Initialize text areas for auto-expand using shared utilities
    SharedUtils.initAutoExpandTextareas();

    // Initialize Bootstrap modals
    const confirmOrderModal = new bootstrap.Modal(document.getElementById('confirmOrderModal'));
    const returnToManagerModal = new bootstrap.Modal(document.getElementById('returnToManagerModal'));
    const rejectOrderModal = new bootstrap.Modal(document.getElementById('rejectOrderModal'));

    // Initialize the OrderDetailsModule with configuration
    OrderDetailsModule.init({
        showLoading: function() {
            document.getElementById('loading').style.display = '';
            document.getElementById('orderDetails').style.display = 'none';
        },
        hideLoading: function() {
            document.getElementById('loading').style.display = 'none';
        },
        showMessage: function(message, type) {
            var alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
            document.getElementById('messageContainer').innerHTML = `<div class="alert ${alertClass}">${message}</div>`;
        },
        showOrderDetails: function() {
            $('#orderDetails').show();
            $('#quickActions').show();
            $('#toggleQuickActions').show();
            $('#orderDetails').addClass('fade-in');
        },
        hideOrderDetails: function() {
            $('#orderDetails').hide();
            $('#quickActions').hide();
            $('#toggleQuickActions').hide();
        }
    });

    // Order selection change handler
    document.getElementById('orderId').addEventListener('change', function() {
        var orderId = this.value;
        if (orderId && orderId !== '') {
            OrderDetailsModule.loadOrderDetails(orderId, '/AssistantManager/GetOrderDetails');
        } else {
            OrderDetailsModule.hideOrderDetails();
        }
    });


    // Confirm order button handler
    document.getElementById('confirmOrderBtn').addEventListener('click', function() {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            confirmOrderModal.show();
        }
    });

    // Return to manager button handler
    document.getElementById('returnToManagerBtn').addEventListener('click', function() {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            var returnReason = document.getElementById('returnReason').value.trim();
            if (!returnReason) {
                OrderDetailsModule.showMessage('يرجى إدخال سبب الإعادة إلى مدير القسم.', 'error');
                return;
            }
            document.getElementById('returnReasonModal').value = returnReason;
            returnToManagerModal.show();
        }
    });

    // Reject order button handler
    document.getElementById('rejectOrderBtn').addEventListener('click', function() {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            var rejectReason = document.getElementById('rejectReason').value.trim();
            if (!rejectReason) {
                OrderDetailsModule.showMessage('يرجى إدخال سبب الإلغاء.', 'error');
                return;
            }
            document.getElementById('rejectReasonModal').value = rejectReason;
            rejectOrderModal.show();
        }
    });

    // Download attachments button handler
    document.getElementById('downloadAttachmentsBtn').addEventListener('click', function() {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            window.location.href = `/AssistantManager/DownloadAttachments?orderId=${currentOrderId}`;
        }
    });

    // Modal confirmation button handlers
    document.getElementById('confirmOrderModalBtn').addEventListener('click', function() {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            confirmOrderModal.hide();
            submitForm('/AssistantManager/ConfirmOrder', { orderId: currentOrderId });
        }
    });

    document.getElementById('returnToManagerModalBtn').addEventListener('click', function() {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            var returnReason = document.getElementById('returnReason').value.trim();
            returnToManagerModal.hide();
            submitForm('/AssistantManager/ReturnToManager', { orderId: currentOrderId, returnReason: returnReason });
        }
    });

    document.getElementById('rejectOrderModalBtn').addEventListener('click', function() {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            var rejectReason = document.getElementById('rejectReason').value.trim();
            rejectOrderModal.hide();
            submitForm('/AssistantManager/RejectOrder', { orderId: currentOrderId, rejectReason: rejectReason });
        }
    });


    function submitForm(url, data) {
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = url;
        form.style.display = 'none';
        
        // Add anti-forgery token
        var token = document.querySelector('input[name="__RequestVerificationToken"]');
        if (token) {
            var tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = '__RequestVerificationToken';
            tokenInput.value = token.value;
            form.appendChild(tokenInput);
        }
        
        // Add data fields
        for (var key in data) {
            var input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = data[key];
            form.appendChild(input);
        }
        
        document.body.appendChild(form);
        form.submit();
    }

    // Show success/error messages from TempData (handled in the view)
    // This will be called from the view with actual TempData values
}); 