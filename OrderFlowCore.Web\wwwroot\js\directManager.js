/**
 * DirectManager View JavaScript
 * Handles order management functionality for the DirectManager view
 */

$(document).ready(function () {
    // Initialize Bootstrap modals
    const confirmOrderModal = new bootstrap.Modal(document.getElementById('confirmOrderModal'));
    const rejectOrderModal = new bootstrap.Modal(document.getElementById('rejectOrderModal'));

    // Initialize the OrderDetailsModule with configuration
    OrderDetailsModule.init({
        showLoading: function() {
            $('#loading').show();
            $('#orderDetails').hide();
            $('#quickActions').hide();
        },
        hideLoading: function() {
            $('#loading').hide();
        },
        showMessage: function(message, type) {
            const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
            const icon = type === 'error' ? '❌' : '✅';
            $('#messageContainer').html(`<div class="alert ${alertClass} fade-in">${icon} ${message}</div>`);
            
            // Auto-hide success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    $('#messageContainer .alert').fadeOut();
                }, 5000);
            }
        },
        showOrderDetails: function() {
            $('#orderDetails').show();
            $('#quickActions').show();
            $('#orderDetails').addClass('fade-in');
        },
        hideOrderDetails: function() {
            $('#orderDetails').hide();
            $('#quickActions').hide();
        }
    });

    // Order selection change handler
    $('#orderSelect').change(function () {
        const orderId = $(this).val();
        if (orderId) {
            OrderDetailsModule.loadOrderDetails(orderId, '/DirectManager/GetOrderDetails');
        } else {
            OrderDetailsModule.hideOrderDetails();
        }
    });


    // Confirm order button handler
    $('#confirmOrderBtn').click(function () {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            confirmOrderModal.show();
        }
    });

    // Reject order button handler
    $('#rejectOrderBtn').click(function () {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            const reason = $('#rejectReason').val();
            if (reason.trim()) {
                $('#rejectReasonModal').val(reason);
                rejectOrderModal.show();
            } else {
                OrderDetailsModule.showMessage('يجب إدخال سبب الإلغاء', 'error');
                $('#rejectReason').focus();
            }
        }
    });

    // Download attachments button handler
    $('#downloadAttachmentsBtn').click(function () {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            window.location.href = `/DirectManager/DownloadAttachments/${currentOrderId}`;
        }
    });

    // Modal confirmation button handlers
    $('#confirmOrderModalBtn').click(function() {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            confirmOrderModal.hide();
            OrderDetailsModule.confirmOrder(
                currentOrderId,
                '/DirectManager/ConfirmOrder',
                'تم تأكيد الطلب بنجاح',
                'حدث خطأ أثناء تأكيد الطلب'
            );
        }
    });

    $('#rejectOrderModalBtn').click(function() {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            const reason = $('#rejectReason').val();
            rejectOrderModal.hide();
            OrderDetailsModule.rejectOrder(
                currentOrderId, 
                reason, 
                '/DirectManager/RejectOrder',
                'تم إلغاء الطلب بنجاح',
                'حدث خطأ أثناء إلغاء الطلب'
            );
        }
    });
}); 