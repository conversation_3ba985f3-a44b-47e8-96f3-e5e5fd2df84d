using OrderFlowCore.Core.Models;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Services
{

    public interface ISupervisorService
    {
        void UpdateSupervisorStatuses(OrdersTable order, List<string> selectedSupervisors, string statusWithDate);
        void ClearUnderImplementationStatuses(OrdersTable order);
        List<string> GetAssignedSupervisors(OrdersTable order);
        string GetSupervisorStatus(OrdersTable order, string supervisorRole);
    }
}