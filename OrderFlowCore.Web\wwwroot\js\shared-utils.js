/**
 * Shared Utilities
 * Common JavaScript functions used across multiple views
 */

const SharedUtils = (function() {
    'use strict';

    /**
     * Initialize auto-expanding textareas
     * @param {string} selector - CSS selector for textareas
     */
    function initAutoExpandTextareas(selector = '.auto-expand') {
        const textAreas = document.querySelectorAll(selector);
        textAreas.forEach(function (textarea) {
            textarea.addEventListener("input", function () {
                this.style.height = "auto";
                this.style.height = (this.scrollHeight) + "px";
            });
        });
    }

    /**
     * Show a toast notification
     * @param {string} message - Message to display
     * @param {string} type - Type of notification ('success', 'error', 'warning', 'info')
     * @param {number} duration - Duration in milliseconds (default: 5000)
     */
    function showToast(message, type = 'info', duration = 5000) {
        const toastContainer = document.getElementById('toastContainer') || createToastContainer();
        
        const toast = document.createElement('div');
        toast.className = `toast toast-${type} show`;
        toast.innerHTML = `
            <div class="toast-header">
                <strong class="me-auto">${getToastTitle(type)}</strong>
                <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
            <div class="toast-body">${message}</div>
        `;
        
        toastContainer.appendChild(toast);
        
        if (duration > 0) {
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, duration);
        }
    }

    /**
     * Create toast container if it doesn't exist
     * @returns {HTMLElement} Toast container element
     */
    function createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
        return container;
    }

    /**
     * Get toast title based on type
     * @param {string} type - Toast type
     * @returns {string} Toast title
     */
    function getToastTitle(type) {
        const titles = {
            'success': 'نجح',
            'error': 'خطأ',
            'warning': 'تحذير',
            'info': 'معلومات'
        };
        return titles[type] || titles.info;
    }

    /**
     * Format date to Arabic locale
     * @param {Date|string} date - Date to format
     * @param {Object} options - Intl.DateTimeFormat options
     * @returns {string} Formatted date
     */
    function formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            ...options
        };
        
        return new Intl.DateTimeFormat('ar-SA', defaultOptions).format(new Date(date));
    }

    /**
     * Format number to Arabic locale
     * @param {number} number - Number to format
     * @param {Object} options - Intl.NumberFormat options
     * @returns {string} Formatted number
     */
    function formatNumber(number, options = {}) {
        const defaultOptions = {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2,
            ...options
        };
        
        return new Intl.NumberFormat('ar-SA', defaultOptions).format(number);
    }

    /**
     * Debounce function execution
     * @param {Function} func - Function to debounce
     * @param {number} wait - Wait time in milliseconds
     * @returns {Function} Debounced function
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Throttle function execution
     * @param {Function} func - Function to throttle
     * @param {number} limit - Time limit in milliseconds
     * @returns {Function} Throttled function
     */
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Copy text to clipboard
     * @param {string} text - Text to copy
     * @returns {Promise<boolean>} Success status
     */
    async function copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (err) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            const success = document.execCommand('copy');
            document.body.removeChild(textArea);
            return success;
        }
    }

    /**
     * Download file from URL
     * @param {string} url - File URL
     * @param {string} filename - Filename for download
     */
    function downloadFile(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    /**
     * Validate email format
     * @param {string} email - Email to validate
     * @returns {boolean} Validation result
     */
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Validate phone number format (Saudi Arabia)
     * @param {string} phone - Phone number to validate
     * @returns {boolean} Validation result
     */
    function isValidPhone(phone) {
        const phoneRegex = /^(05|5)(5|0|3|6|4|9|1|8|7)([0-9]{7})$/;
        return phoneRegex.test(phone.replace(/\s/g, ''));
    }

    /**
     * Generate random ID
     * @param {number} length - Length of ID (default: 8)
     * @returns {string} Random ID
     */
    function generateId(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    /**
     * Add CSS styles dynamically
     * @param {string} css - CSS rules
     * @param {string} id - Style element ID (optional)
     */
    function addStyles(css, id = null) {
        const style = document.createElement('style');
        if (id) style.id = id;
        style.textContent = css;
        document.head.appendChild(style);
    }

    /**
     * Remove CSS styles by ID
     * @param {string} id - Style element ID
     */
    function removeStyles(id) {
        const style = document.getElementById(id);
        if (style) {
            style.remove();
        }
    }

    // Public API
    return {
        initAutoExpandTextareas,
        showToast,
        formatDate,
        formatNumber,
        debounce,
        throttle,
        copyToClipboard,
        downloadFile,
        isValidEmail,
        isValidPhone,
        generateId,
        addStyles,
        removeStyles
    };
})();

// Make it available globally
window.SharedUtils = SharedUtils; 

SharedUtils.showToast = function(message, type) {
    var toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) return;
    var toastId = 'toast_' + Date.now() + '_' + Math.floor(Math.random()*10000);
    var alertClass = 'bg-primary text-white';
    if (type === 'success') alertClass = 'bg-success text-white';
    else if (type === 'error' || type === 'danger') alertClass = 'bg-danger text-white';
    else if (type === 'info') alertClass = 'bg-info text-white';
    var toast = document.createElement('div');
    toast.className = 'toast align-items-center ' + alertClass;
    toast.id = toastId;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    toast.style.minWidth = '250px';
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body w-100">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    toastContainer.appendChild(toast);
    var bsToast = new bootstrap.Toast(toast, { delay: 4000 });
    bsToast.show();
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}; 